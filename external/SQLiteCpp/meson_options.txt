# Options relative to SQLite and SQLiteC++ functions
## Enable the use of SQLite column metadata and Column::getColumnOriginName() method,
## Require that the sqlite3 library is also compiled with this flag (default under Debian/Ubuntu, but not on Mac OS X).
option('SQLITE_ENABLE_COLUMN_METADATA', type: 'boolean', value: false, description: 'Enable Column::getColumnOriginName(). Require support from sqlite3 library.')
## Enable the user definition of a assertion_failed() handler (default to false, easier to handler for beginners).
option('SQLITE_ENABLE_ASSERT_HANDLER', type: 'boolean', value: false, description: 'Enable the user definition of a assertion_failed() handler.')
## Enable database encryption API. Requires implementations of sqlite3_key & sqlite3_key_v2.
## Eg. SQLCipher (libsqlcipher-dev) is an SQLite extension that provides 256 bit AES encryption of database files. 
option('SQLITE_HAS_CODEC', type: 'boolean', value: false, description: 'Enable database encryption API. Not available in the public release of SQLite.')
## Force forward declaration of legacy struct sqlite3_value (pre SQLite 3.19)
option('SQLITE_USE_LEGACY_STRUCT', type: 'boolean', value: false, description: 'Fallback to forward declaration of legacy struct sqlite3_value (pre SQLite 3.19)')
## Enable ommit load extension
option('SQLITE_OMIT_LOAD_EXTENSION', type: 'boolean', value: false, description: 'Enable ommit load extension.')
## Disable the support for std::filesystem (C++17)
option('SQLITECPP_DISABLE_STD_FILESYSTEM', type: 'boolean', value: false, description: 'Disable the support for std::filesystem (C++17)')
## Disable the support for sqlite3_expanded_sql (since SQLite 3.14.0)
option('SQLITECPP_DISABLE_SQLITE3_EXPANDED_SQL', type: 'boolean', value: false, description: 'Disable the support for sqlite3_expanded_sql (since SQLite 3.14.0)')
## Stack protection is not supported on MinGW-W64 on Windows, allow this flag to be turned off.
option('SQLITECPP_USE_STACK_PROTECTION', type: 'boolean', value: true, description: 'Enable stack protection for MySQL.')
## Enable build for the tests of SQLiteC++
option('SQLITECPP_BUILD_TESTS', type: 'boolean', value: false, description: 'Build SQLiteC++ unit tests.')
## Build the examples of SQLiteC++
option('SQLITECPP_BUILD_EXAMPLES', type: 'boolean', value: false, description: 'Build SQLiteC++ examples.')
