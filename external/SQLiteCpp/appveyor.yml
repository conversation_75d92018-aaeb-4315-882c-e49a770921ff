# Copyright (c) 2012-2023 Sebas<PERSON> (<EMAIL>)

# build format
version: "{build}"

# scripts that run after cloning repository
install:
  - git submodule update --init --recursive
 
image:
# Note: reduced the number of images to test on <PERSON><PERSON><PERSON><PERSON><PERSON>, to speed up the build
  - Visual Studio 2022
#  - Visual Studio 2019
#  - Visual Studio 2017
  - Visual Studio 2015
  
# configurations to add to build matrix
configuration:
  - Debug
  - Release

environment:
  matrix:
  - arch: Win32
  - arch: x64

init:
  - echo %APPVEYOR_BUILD_WORKER_IMAGE% - %configuration% - %arch%
  - if "%APPVEYOR_BUILD_WORKER_IMAGE%"=="Visual Studio 2017" (set vs=Visual Studio 15 2017)
  - if "%APPVEYOR_BUILD_WORKER_IMAGE%"=="Visual Studio 2015" (set vs=Visual Studio 14 2015)
  - if "%arch%"=="x64" (set generator="%vs% Win64") else (set generator="%vs%")
  # CMake uses a different grammar for Visual Studio 2019, with -A to specify architecture:
  - if "%APPVEYOR_BUILD_WORKER_IMAGE%"=="Visual Studio 2019" (set generator="Visual Studio 16 2019" -A %arch%)
  - if "%APPVEYOR_BUILD_WORKER_IMAGE%"=="Visual Studio 2022" (set generator="Visual Studio 17 2022" -A %arch%)
  - echo %generator%
 
# scripts to run before build
before_build:
  - mkdir build
  - cd build
  - cmake -DCMAKE_VERBOSE_MAKEFILE=ON -DSQLITECPP_BUILD_EXAMPLES=ON -DSQLITECPP_BUILD_TESTS=ON -DSQLITECPP_RUN_CPPCHECK=OFF .. -G %generator%

# build examples, and run tests (ie make & make test)
build_script:
  - cmake --build . --config %configuration%
  - ctest --output-on-failure
