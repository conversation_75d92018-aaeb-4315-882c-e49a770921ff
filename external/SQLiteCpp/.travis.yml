# Copyright (c) 2012-2023 Sebastien Rombauts (<EMAIL>)

language: cpp

# Use Linux unless specified otherwise
os: linux

cache:
  apt: true

env:
  global:
    - BUILD_TYPE=Debug
    - ASAN=ON
    - INTERNAL_SQLITE=ON
    - VALGRIND=OFF
    - TESTS=ON
    - SHARED_LIBS=OFF

# Build variants (should test a reasonable number of combination of CMake options)
jobs:
  include:

    ##########################################################################
    # GCC on Linux
    ##########################################################################

    # GCC 11.2.0 (Ubuntu Jammy 22.04)
    - dist: jammy
      env:
        - cc=gcc cxx=g++

    # Clang 9.3.0 (Ubuntu Focal 20.04)
    - dist: focal
      env:
        - cc=gcc cxx=g++

    # Coverity static code analysis (Ubuntu Bionic 18.04)
    - dist: bionic
      env:
        - COVERITY_SCAN_PROJECT_NAME=SRombauts/SQLiteCpp
        - COVERITY_SCAN_BRANCH_PATTERN=master
        - COVERITY_SCAN_NOTIFICATION_EMAIL=<EMAIL>
        - COVERITY_SCAN_BUILD_COMMAND_PREPEND="cmake ."
        - COVERITY_SCAN_BUILD_COMMAND="make -j8"
        # Encrypted COVERITY_SCAN_TOKEN, created via the "travis encrypt" command using the project repo's public key
        - secure: "Qm4d8NEDPBtYZCYav46uPEvDCtaRsjLXlkVS+C+WCJAPcwXCGkrr96wEi7RWcq2xD86QCh0XiqaPT+xdUmlohOYIovRhaaBmZ1lwIJ4GsG/ZR6xoFr3DYsZ3o4GyXk2vNXNxEl82AC+Xs6e6gkLOV9XRkBcjpVIvoIXgNlKWeGY="

    # GCC 7.4.0 Debug build with GCov for coverage build (Ubuntu Bionic 18.04)
    - dist: bionic
      env:
        - cc=gcc cxx=g++
        - GCOV=ON
        - COVERALLS=ON

    # GCC 7.4.0 Debug build with Valgrind instead of Address Sanitizer (Ubuntu Bionic 18.04)
    - dist: bionic
      env:
        - cc=gcc cxx=g++
        - ASAN=OFF
        - VALGRIND=ON

    # GCC 7.4.0 Release build (Ubuntu Bionic 18.04)
    - dist: bionic
      env:
        - cc=gcc cxx=g++
        - BUILD_TYPE=Release

    # GCC 7.4.0 Shared Libs (Ubuntu Bionic 18.04)
    - dist: bionic
      env:
        - cc=gcc cxx=g++
        - SHARED_LIBS=ON

    # GCC 7.4.0 test linking with libsqlite3-dev package (Ubuntu Bionic 18.04)
    - dist: bionic
      env:
        - cc=gcc cxx=g++
        - INTERNAL_SQLITE=OFF

    # GCC 5.4.0 (Ubuntu Xenial 16.04)
    - dist: xenial # Default
      env:
        - cc=gcc cxx=g++

    # GCC 4.8.4 (Ubuntu Trusty 14.04)
    - dist: trusty
      env:
        - cc=gcc cxx=g++
        - TESTS=OFF

    ##########################################################################
    # Clang on Linux
    ##########################################################################

    # Clang 7.0.0 (Ubuntu Jammy 22.04)
    - dist: jammy
      env:
        - cc=clang cxx=clang++

    # Clang 7.0.0 (Ubuntu Focal 20.04)
    - dist: focal
      env:
        - cc=clang cxx=clang++

    # Clang 7.0.0 (Ubuntu Bionic 18.04)
    - dist: bionic
      env:
        - cc=clang cxx=clang++

    # Clang 7.0.0 (Ubuntu Xenial 16.04) 
    - dist: xenial # Default
      env:
        - cc=clang cxx=clang++

    # Clang 5.0.0 (Ubuntu Trusty 14.04)
    - dist: trusty
      env:
        - cc=clang cxx=clang++
        - TESTS=OFF

    ##########################################################################
    # Clang on OSX
    ##########################################################################

    # XCode 14.2 - Apple clang 14.0.0 - macOS 12.6
    - os: osx
      osx_image: xcode14.2
      env:
        - cc=clang cxx=clang++

    # Default XCode - Apple clang 9.1.0 - macOS 10.13
    - os: osx
      osx_image: xcode9.4 # Default
      env:
        - cc=clang cxx=clang++

    # XCode 8.3 - Applec lang 8.1.0 - macOS 10.12
    - os: osx
      osx_image: xcode8.3
      env:
        - cc=clang cxx=clang++
        - TESTS=OFF

before_install:
  # Coverity: don't use addons.coverity_scan since it run on every job of the build matrix, which waste resources and exhausts quotas
  # Note: the job dedicated to Coverity need to only run the shell script and then exit (to not try to build and run unit tests etc.)
  - if [[ -n "$COVERITY_SCAN_PROJECT_NAME" ]] ; then curl -s https://scan.coverity.com/scripts/travisci_build_coverity_scan.sh | bash ; exit 0 ; fi

  - if [[ "$INTERNAL_SQLITE" == "OFF" ]]; then sudo apt-get install libsqlite3-dev ; fi
  - if [[ "$VALGRIND" == "ON" ]]; then sudo apt-get install valgrind ; fi
  - if [[ "$COVERALLS" == "ON" ]]; then pip install --user cpp-coveralls ; fi

    # Set the compiler environment variables properly
  - export CC=${cc}
  - export CXX=${cxx}

# scripts to run before build
before_script:
  - mkdir build
  - cd build
  - cmake -DCMAKE_VERBOSE_MAKEFILE=ON -DCMAKE_BUILD_TYPE=$BUILD_TYPE -DBUILD_SHARED_LIBS=$SHARED_LIBS -DSQLITECPP_INTERNAL_SQLITE=$INTERNAL_SQLITE -DSQLITECPP_USE_ASAN=$ASAN -DSQLITECPP_USE_GCOV=$GCOV -DSQLITECPP_BUILD_EXAMPLES=$TESTS -DSQLITECPP_BUILD_TESTS=$TESTS ..

# build examples, and run tests (ie make & make test)
script:
  - cmake --build .
  - export ASAN_OPTIONS=verbosity=1:debug=1
  - if [[ "$TESTS" == "ON" ]]; then ctest --verbose --output-on-failure ; fi
  - if [[ "$VALGRIND" == "ON" ]]; then valgrind --leak-check=full --error-exitcode=1 bin/SQLiteCpp_example1 ; fi
  - if [[ "$VALGRIND" == "ON" ]]; then valgrind --leak-check=full --error-exitcode=1 bin/SQLiteCpp_tests ; fi

# generate and publish GCov coveralls results
after_success:
  - if [[ "$COVERALLS" == "ON" ]]; then coveralls --root .. -e examples -e googletest -e sqlite3 -e tests -E ".*feature_tests.*" -E ".*CompilerId.*" --gcov-options '\-lp' ; fi
