<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="cdt.managedbuild.config.gnu.exe.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.config.gnu.exe.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe" cleanCommand="rm -rf" description="" id="cdt.managedbuild.config.gnu.exe.debug.**********" name="Debug" parent="cdt.managedbuild.config.gnu.exe.debug">
					<folderInfo id="cdt.managedbuild.config.gnu.exe.debug.**********." name="/" resourcePath="">
						<toolChain id="cdt.managedbuild.toolchain.gnu.exe.debug.898681687" name="Linux GCC" nonInternalBuilderId="cdt.managedbuild.target.gnu.builder.exe.debug" superClass="cdt.managedbuild.toolchain.gnu.exe.debug">
							<targetPlatform id="cdt.managedbuild.target.gnu.platform.exe.debug.25715897" name="Debug Platform" superClass="cdt.managedbuild.target.gnu.platform.exe.debug"/>
							<builder buildPath="${ProjDirPath}/build" id="cdt.managedbuild.target.gnu.builder.exe.debug.1103730408" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="cdt.managedbuild.target.gnu.builder.exe.debug"/>
							<tool id="cdt.managedbuild.tool.gnu.archiver.base.836634439" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
							<tool command="g++" id="cdt.managedbuild.tool.gnu.cpp.compiler.exe.debug.**********" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.exe.debug">
								<option id="gnu.cpp.compiler.exe.debug.option.optimization.level.750523151" name="Optimization Level" superClass="gnu.cpp.compiler.exe.debug.option.optimization.level" value="gnu.cpp.compiler.optimization.level.none" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.exe.debug.option.debugging.level.1248186067" name="Debug Level" superClass="gnu.cpp.compiler.exe.debug.option.debugging.level" value="gnu.cpp.compiler.debugging.level.max" valueType="enumerated"/>
								<inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.**********" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.compiler.exe.debug.796464367" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.exe.debug">
								<option defaultValue="gnu.c.optimization.level.none" id="gnu.c.compiler.exe.debug.option.optimization.level.1202477623" name="Optimization Level" superClass="gnu.c.compiler.exe.debug.option.optimization.level" valueType="enumerated"/>
								<option id="gnu.c.compiler.exe.debug.option.debugging.level.280470620" name="Debug Level" superClass="gnu.c.compiler.exe.debug.option.debugging.level" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.**********" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.linker.exe.debug.1058275134" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.exe.debug"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.exe.debug.1061662206" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.exe.debug">
								<option id="gnu.cpp.link.option.libs.1966761747" name="Libraries (-l)" superClass="gnu.cpp.link.option.libs" valueType="libs">
									<listOptionValue builtIn="false" value="pthread"/>
									<listOptionValue builtIn="false" value="dl"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.1636986986" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.assembler.exe.debug.258570404" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.exe.debug">
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.1571334436" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="org.eclipse.cdt.core.language.mapping"/>
			<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings">
				<doc-comment-owner id="org.eclipse.cdt.ui.doxygen">
					<path value=""/>
				</doc-comment-owner>
			</storageModule>
		</cconfiguration>
		<cconfiguration id="cdt.managedbuild.config.gnu.exe.release.326780594">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.config.gnu.exe.release.326780594" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release,org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe" cleanCommand="rm -rf" description="" id="cdt.managedbuild.config.gnu.exe.release.326780594" name="Release" parent="cdt.managedbuild.config.gnu.exe.release">
					<folderInfo id="cdt.managedbuild.config.gnu.exe.release.326780594." name="/" resourcePath="">
						<toolChain id="cdt.managedbuild.toolchain.gnu.exe.release.1521127462" name="Linux GCC" superClass="cdt.managedbuild.toolchain.gnu.exe.release">
							<targetPlatform id="cdt.managedbuild.target.gnu.platform.exe.release.1294814790" name="Debug Platform" superClass="cdt.managedbuild.target.gnu.platform.exe.release"/>
							<builder arguments="BUILD=Release" buildPath="${ProjDirPath}" command="make" id="cdt.managedbuild.target.gnu.builder.exe.release.1733496537" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="cdt.managedbuild.target.gnu.builder.exe.release"/>
							<tool id="cdt.managedbuild.tool.gnu.archiver.base.446006787" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.compiler.exe.release.**********" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.exe.release">
								<option id="gnu.cpp.compiler.exe.release.option.optimization.level.1921346334" name="Optimization Level" superClass="gnu.cpp.compiler.exe.release.option.optimization.level" value="gnu.cpp.compiler.optimization.level.most" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.exe.release.option.debugging.level.1660521780" name="Debug Level" superClass="gnu.cpp.compiler.exe.release.option.debugging.level" value="gnu.cpp.compiler.debugging.level.none" valueType="enumerated"/>
								<inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.**********" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.compiler.exe.release.**********" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.exe.release">
								<option defaultValue="gnu.c.optimization.level.most" id="gnu.c.compiler.exe.release.option.optimization.level.2130170048" name="Optimization Level" superClass="gnu.c.compiler.exe.release.option.optimization.level" valueType="enumerated"/>
								<option id="gnu.c.compiler.exe.release.option.debugging.level.1028604453" name="Debug Level" superClass="gnu.c.compiler.exe.release.option.debugging.level" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.**********" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.linker.exe.release.1484631410" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.exe.release"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.exe.release.1493084285" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.exe.release">
								<inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.1398815353" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.assembler.exe.release.1362356526" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.exe.release">
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.831278578" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.language.mapping"/>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings">
				<doc-comment-owner id="org.eclipse.cdt.ui.doxygen">
					<path value=""/>
				</doc-comment-owner>
			</storageModule>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="SQLiteC++.cdt.managedbuild.target.gnu.exe.2007535171" name="Executable" projectType="cdt.managedbuild.target.gnu.exe"/>
	</storageModule>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/SQLiteCpp"/>
		</configuration>
		<configuration configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="/SQLiteCpp"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="makefileGenerator">
				<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.exe.release.326780594;cdt.managedbuild.config.gnu.exe.release.326780594.;cdt.managedbuild.tool.gnu.c.compiler.exe.release.**********;cdt.managedbuild.tool.gnu.c.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="makefileGenerator">
					<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.exe.debug.**********;cdt.managedbuild.config.gnu.exe.debug.**********.;cdt.managedbuild.tool.gnu.cpp.compiler.exe.debug.**********;cdt.managedbuild.tool.gnu.cpp.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="makefileGenerator">
					<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.exe.release.326780594;cdt.managedbuild.config.gnu.exe.release.326780594.;cdt.managedbuild.tool.gnu.cpp.compiler.exe.release.**********;cdt.managedbuild.tool.gnu.cpp.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="makefileGenerator">
					<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.exe.debug.**********;cdt.managedbuild.config.gnu.exe.debug.**********.;cdt.managedbuild.tool.gnu.c.compiler.exe.debug.796464367;cdt.managedbuild.tool.gnu.c.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerFileProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="makefileGenerator">
					<runAction arguments="-E -P -v -dD" command="" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/${specs_file}&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'g++ -E -P -v -dD &quot;${plugin_state_location}/specs.cpp&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCWinManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-c 'gcc -E -P -v -dD &quot;${plugin_state_location}/specs.c&quot;'" command="sh" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
</cproject>
