cmake_minimum_required(VERSION 3.10)

project(QFaceEngine_demo)

# Set executable file name
set(EXECUTABLE_FILE_NAME "QFaceEngine_demo_CPP")

# Set source directory
set(QFE_DEMO_SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)

# Set include directory
set(QFE_DEMO_INC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)

# Get all source files
file(GLOB_RECURSE QFE_DEMO_SRC_FILES ${QFE_DEMO_SRC_DIR}/*.c*)

# Find package
find_package(Threads REQUIRED)

# Find OpenCV
find_package(OpenCV REQUIRED)
include_directories(${OpenCV_INCLUDE_DIRS})

# QFaceEngine SDK 헤더 경로 추가
include_directories(${CMAKE_SOURCE_DIR}/include)

message(STATUS "[DEMO] Runtime output directory: ${CMAKE_RUNTIME_OUTPUT_CPP_DIRECTORY}")

# Add executable files
add_executable(${EXECUTABLE_FILE_NAME} ${QFE_DEMO_SRC_FILES})

# Set output directory
set_target_properties(${EXECUTABLE_FILE_NAME} PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_CPP_DIRECTORY})

# Add include directories
target_include_directories(${EXECUTABLE_FILE_NAME} PRIVATE
        ${QFE_DEMO_INC_DIR}
)

# target_link_libraries(${EXECUTABLE_FILE_NAME} PUBLIC QFE_SDK QFE_CORE Threads::Threads)
target_link_libraries(${EXECUTABLE_FILE_NAME} PUBLIC QFE_SDK QFE_CORE Threads::Threads ${OpenCV_LIBS})