#ifndef __TEST_GLOBAL_H__
#define __TEST_GLOBAL_H__

#include <iostream>
#include <chrono>
#include <string>
#include <random>
#include <functional>
#ifdef _WIN32
#include <windows.h>
#else
#include <sys/sysinfo.h>
#endif

#include "QFE_API.h"
#include "QFE_API.hpp"

typedef std::chrono::milliseconds ms;

#define MODEL_PATH "model"
#define LICENSE_PATH "license/linux_license.dat"
#define IMG_RGB_PATH "test_image/test_rgb_image/test_rgb_image.jpg"
#define IMG_RGB_PATH2 "test_image/test_rgb_image/iu_test.jpg"
#define IMG_IR_PATH "test_image/test_ir_image/test_ir_image.jpg"
#define WRAP_IMG_RGB_PATH "test_image/test_rgb_image/test_rgb_wrap_image.jpg"
#define WRAP_IMG_IR_PATH "test_image/test_ir_image/test_ir_wrap_image.jpg"
#define MS_RGB_PATH "test_image/test_rgb_image/test_rgb_mask_segmentation.jpg"
#define MS_IR_PATH "test_image/test_ir_image/test_ir_mask_segmentation.jpg"
#define TEMPLATE_RGB_PATH "test_image/test_rgb_image/test_rgb_template.dat"
#define TEMPLATE_IR_PATH "test_image/test_ir_image/test_ir_template.dat"
#define GALLERY_TEMPLATE_PATH "test_template"
#define GALLERY_IMAGE_PATH "test_image"
#define TEST_GALLERY_IMAGE_PATH "test_gallery_image"
#define IMG_WRITE_TEST_PATH "test.jpg"
#define MS_TEST_PATH "test_image/ms_test_image.jpg"
#define MULTI_FACE_TEST_PATH "test_image/multi_fd_test_image.jpg"

#define BASE64_IMG_PATH "test_image/image_base64.txt"
#define BASE64_TEMPLATE_PATH "test_image/template_base64.txt"

#define DATABASE_PATH "test_database/qfe_test.db"
#define FULL_DATABASE_PATH "test_database/qfe_full.db"

extern QFE_SDK *sdk_instance;

extern int number_of_mode;
extern int number_of_model_instances;
extern int number_of_thread_for_test;
extern bool is_use_gpu;
extern bool is_detail_log;
extern bool is_multi_thread;
extern long multi_thread_time;

void print_memory_check();
void print_function_name(const char* _func);
void print_function_name_for_thread(const char* _func);
void print_function_name_force(const char* _func);
void Test_InitSDK();

#endif