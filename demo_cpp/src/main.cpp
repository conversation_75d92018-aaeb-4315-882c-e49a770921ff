#include "core_api.h"

#include <iostream>
#include <vector>
#include <chrono>
#include <sys/stat.h>  // for mkdir
#include <sys/types.h> // for mkdir
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
#include <filesystem>
#include <algorithm>   // for transform
#include <fstream>
#include <iomanip>

#define MAX_FACE 10
typedef std::chrono::milliseconds ms;
using namespace std::chrono;

// 전역 변수로 처리 시간 저장
long long total_duration_fd = 0;
long long total_inference_time = 0;
long long total_postprocess_time = 0;
int total_processed_images = 0;
struct fcore_Tracker* tracker_instance = NULL;

// 성능 정보 파싱을 위한 전역 변수
long long total_inference_microseconds = 0;
long long total_postprocess_microseconds = 0;

// 전역 변수로 입력 디렉토리 저장
std::string input_image_dir;

// IoU 계산 함수
float calculateIoU(const int bbox1[4], const int bbox2[4]) {
    int bbox1_x1 = bbox1[0];
    int bbox1_y1 = bbox1[1];
    int bbox1_x2 = bbox1[0] + bbox1[2];
    int bbox1_y2 = bbox1[1] + bbox1[3];
    
    int bbox2_x1 = bbox2[0];
    int bbox2_y1 = bbox2[1];
    int bbox2_x2 = bbox2[0] + bbox2[2];
    int bbox2_y2 = bbox2[1] + bbox2[3];

    int x1 = std::max(bbox1_x1, bbox2_x1);
    int y1 = std::max(bbox1_y1, bbox2_y1);
    int x2 = std::min(bbox1_x2, bbox2_x2);
    int y2 = std::min(bbox1_y2, bbox2_y2);

    int intersection_width = std::max(0, x2 - x1);
    int intersection_height = std::max(0, y2 - y1);
    int intersection_area = intersection_width * intersection_height;

    int bbox1_area = bbox1[2] * bbox1[3];  // width * height
    int bbox2_area = bbox2[2] * bbox2[3];  // width * height

    int union_area = bbox1_area + bbox2_area - intersection_area;

    if (union_area == 0) {
        return 0.0f;
    }

    return static_cast<float>(intersection_area) / union_area;
}

// 가장 유사한 바운딩 박스 찾기
int findMostSimilarBbox(const int* tracker_bbox, int num_face, const int fd_bbox[4]) {
    int most_similar_j = -1;
    float highest_iou = 0.0f;

    for (int j = 0; j < num_face; j++) {
        int temp_tracker_bbox[4];
        memcpy(temp_tracker_bbox, &tracker_bbox[j * 4], sizeof(int) * 4);

        float iou = calculateIoU(temp_tracker_bbox, fd_bbox);

        if (iou > highest_iou) {
            highest_iou = iou;
            most_similar_j = j;
        }
    }

    return most_similar_j;
}

// 결과 이미지를 저장할 디렉토리 생성 함수
void create_output_directory() {
    mkdir("test_result", 0777);
}

// 파일 확장자 가져오기
std::string get_extension(const std::string& filename) {
    size_t pos = filename.find_last_of(".");
    if (pos == std::string::npos) return "";
    std::string ext = filename.substr(pos);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    return ext;
}

// 성능 통계 출력 함수
void print_performance_stats() {
    if (total_processed_images > 0) {
        double avg_total_time = static_cast<double>(total_duration_fd) / total_processed_images;
        double avg_inference_time = static_cast<double>(total_inference_microseconds) / total_processed_images / 1000.0;
        double avg_postprocess_time = static_cast<double>(total_postprocess_microseconds) / total_processed_images / 1000.0;
        
        std::cout << "\n[PERFORMANCE] ========== FINAL PERFORMANCE STATISTICS ==========" << std::endl;
        std::cout << "[PERFORMANCE] Total images processed: " << total_processed_images << std::endl;
        std::cout << "[PERFORMANCE] Average detection time: " << std::fixed << std::setprecision(2) 
                  << avg_total_time << " ms" << std::endl;
        std::cout << "[PERFORMANCE] Average inference time: " << std::fixed << std::setprecision(2) 
                  << avg_inference_time << " ms" << std::endl;
        std::cout << "[PERFORMANCE] Average postprocess time: " << std::fixed << std::setprecision(2) 
                  << avg_postprocess_time << " ms" << std::endl;
        
        // FPS 계산
        if (avg_total_time > 0) {
            double fps = 1000.0 / avg_total_time;
            std::cout << "[PERFORMANCE] Average FPS: " << std::fixed << std::setprecision(2) 
                      << fps << " frames/second" << std::endl;
        }
        
        std::cout << "[PERFORMANCE] ==================================================" << std::endl;
    } else {
        std::cout << "[PERFORMANCE] No images processed yet." << std::endl;
    }
}

// 성능 정보 파싱 함수
void parse_performance_output(const std::string& output) {
    // Inference 시간 파싱
    size_t inference_pos = output.find("Inference (detect_yolo_square) time:");
    if (inference_pos != std::string::npos) {
        size_t start = output.find(":", inference_pos) + 1;
        size_t end = output.find("microseconds", start);
        if (end != std::string::npos) {
            std::string time_str = output.substr(start, end - start);
            // 공백 제거
            time_str.erase(std::remove_if(time_str.begin(), time_str.end(), ::isspace), time_str.end());
            try {
                long long inference_time = std::stoll(time_str);
                total_inference_microseconds += inference_time;
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Failed to parse inference time: " << time_str << std::endl;
            }
        }
    }
    
    // Postprocessing 시간 파싱
    size_t postprocess_pos = output.find("Postprocessing (detect_yolo_postprocess) time:");
    if (postprocess_pos != std::string::npos) {
        size_t start = output.find(":", postprocess_pos) + 1;
        size_t end = output.find("microseconds", start);
        if (end != std::string::npos) {
            std::string time_str = output.substr(start, end - start);
            // 공백 제거
            time_str.erase(std::remove_if(time_str.begin(), time_str.end(), ::isspace), time_str.end());
            try {
                long long postprocess_time = std::stoll(time_str);
                total_postprocess_microseconds += postprocess_time;
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Failed to parse postprocess time: " << time_str << std::endl;
            }
        }
    }
}

// 성능 통계 초기화 함수
void reset_performance_stats() {
    total_duration_fd = 0;
    total_processed_images = 0;
    total_inference_microseconds = 0;
    total_postprocess_microseconds = 0;
    std::cout << "[PERFORMANCE] Performance statistics reset." << std::endl;
}

// 디렉토리 내 파일 목록 가져오기 (하위 폴더 포함)
std::vector<std::string> get_files_in_directory(const std::string& dir_path) {
    std::vector<std::string> files;
    try {
        for (const auto& entry : std::filesystem::recursive_directory_iterator(dir_path)) {
            if (entry.is_regular_file()) {
                std::string extension = entry.path().extension().string();
                std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
                if (extension == ".jpg" || extension == ".jpeg" || extension == ".png" || extension == ".mp4") {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "Error accessing directory " << dir_path << ": " << e.what() << std::endl;
    }
    
    // 파일 경로 정렬
    std::sort(files.begin(), files.end());
    
    std::cout << "Found " << files.size() << " media files in " << dir_path << " and its subdirectories" << std::endl;
    return files;
}

// 프레임 처리 함수 (이미지/동영상 공용)
int process_frame(const std::string& input_path, int frame_number = -1) {
    std::cout << "[DEBUG] Starting process_frame for: " << input_path << std::endl;

    // 이미지 로드
    unsigned char *in_image_heap = NULL;
    int in_image_height = 0, in_image_width = 0, in_image_channel = 0;
    std::cout << "[DEBUG] Calling util_imread..." << std::endl;
    int ret = util_imread(&in_image_heap, &in_image_height, &in_image_width, &in_image_channel, (char*)input_path.c_str());
    std::cout << "[DEBUG] util_imread returned: " << ret << std::endl;
    std::cout << "[DEBUG] Image properties - Height: " << in_image_height 
              << ", Width: " << in_image_width 
              << ", Channels: " << in_image_channel << std::endl;

    if (ret != 0) {
        std::cerr << "Failed to read image: " << input_path << std::endl;
        return -1;
    }

    if (in_image_heap == NULL || in_image_height <= 0 || in_image_width <= 0 || in_image_channel <= 0) {
        std::cerr << "Invalid image properties: " << input_path << std::endl;
        if (in_image_heap) free(in_image_heap);
        return -1;
    }

    // initialize output variables according to number of "MAX_FACE"
    int landmarks[MAX_FACE * 10] = {0};
    int bboxes[MAX_FACE * 4] = {0};
    float scores[MAX_FACE] = {0.0};
    int num_face = 0;
    struct IQ iqs[MAX_FACE] = {0};

    // Tracker 관련 변수 선언
    struct fcore_Tracker* out_tracks = NULL;
    int out_num_track = 0;

    std::cout << "[DEBUG] Allocating warped_images..." << std::endl;
    unsigned char **warped_images = (unsigned char **)malloc(MAX_FACE * sizeof(unsigned char *));
    if (!warped_images) {
        std::cerr << "Memory allocation failed for warped_images\n";
        free(in_image_heap);
        return -1;
    }

    // warped_images 배열 초기화
    for (int i = 0; i < MAX_FACE; i++) {
        warped_images[i] = NULL;
    }

    ///////////////////////////////////////////////////////////
    //                      Detection                        //
    ///////////////////////////////////////////////////////////

    std::cout << "[DEBUG] Starting face detection..." << std::endl;
    auto detection_start = high_resolution_clock::now();

    ret = fcore_detect_yolo(
        warped_images, &num_face, scores, landmarks, bboxes,
        iqs, in_image_heap, in_image_height, in_image_width, MAX_FACE);

    auto detection_end = high_resolution_clock::now();
    auto duration_fd = duration_cast<milliseconds>(detection_end - detection_start).count();
    total_duration_fd += duration_fd;
    total_processed_images++;
    
    // core_api에서 측정된 성능 정보 가져오기
    long long inference_time, postprocess_time;
    get_last_performance_times(&inference_time, &postprocess_time);
    total_inference_microseconds += inference_time;
    total_postprocess_microseconds += postprocess_time;
    
    // 현재 이미지의 성능 정보 출력
    printf("Detected %d face. Time: %ld ms\n", num_face, duration_fd);
    printf("[PERFORMANCE] Current inference time: %.2f ms, postprocess time: %.2f ms\n", 
           inference_time / 1000.0, postprocess_time / 1000.0);
    
    // 누적 평균 계산 및 출력
    if (total_processed_images > 0) {
        double avg_time = static_cast<double>(total_duration_fd) / total_processed_images;
        double avg_inference = static_cast<double>(total_inference_microseconds) / total_processed_images / 1000.0;
        double avg_postprocess = static_cast<double>(total_postprocess_microseconds) / total_processed_images / 1000.0;
        printf("[PERFORMANCE] Current averages (%d images) - Total: %.2f ms, Inference: %.2f ms, Postprocess: %.2f ms\n", 
               total_processed_images, avg_time, avg_inference, avg_postprocess);
    }

    std::cout << "[DEBUG] fcore_detect_yolo returned: " << ret << std::endl;
    std::cout << "[DEBUG] Number of faces detected: " << num_face << std::endl;

    // 바운딩 박스 정보 출력
    std::cout << "\n[DEBUG] Bounding Box Information:" << std::endl;
    for (int i = 0; i < num_face; i++) {
        std::cout << "Face " << i << ":" << std::endl;
        std::cout << "\t- Bbox (x1, y1, x2, y2): " 
                  << bboxes[i*4] << ", " 
                  << bboxes[i*4+1] << ", " 
                  << bboxes[i*4+2] << ", " 
                  << bboxes[i*4+3] << std::endl;
        std::cout << "\t- Confidence Score: " << scores[i] << std::endl;
        
        // YOLO 형식으로 변환된 좌표 출력
        float x1 = bboxes[i*4];
        float y1 = bboxes[i*4+1];
        float x2 = bboxes[i*4+2];
        float y2 = bboxes[i*4+3];
        
        // YOLO 포맷 변환 (이미지 크기로 나누기)
        float x_center = (x1 + x2) / 2;
        float y_center = (y1 + y2) / 2;
        float width = x2;
        float height = y2;
        
        std::cout << "\t- YOLO format (x_center, y_center, width, height): "
                  << std::fixed << std::setprecision(5)
                  << x_center << ", "
                  << y_center << ", "
                  << width << ", "
                  << height << std::endl;
                  
        // YOLO -> 절대 좌표 역변환 (이미지 크기로 곱하기)
        float reverse_x1 = x_center - (width / 2);
        float reverse_y1 = y_center - (height / 2);
        float reverse_x2 = reverse_x1 + width;
        float reverse_y2 = reverse_y1 + height;
        
        std::cout << "\t- Reverse conversion (x1, y1, x2, y2): "
                  << reverse_x1 << ", "
                  << reverse_y1 << ", "
                  << reverse_x2 << ", "
                  << reverse_y2 << std::endl;
                  
        // 최종 이미지 크기 적용
        std::cout << "\t- Final coordinates (x1, y1, x2, y2): "
                  << reverse_x1 * in_image_width << ", "
                  << reverse_y1 * in_image_height << ", "
                  << reverse_x2 * in_image_width << ", "
                  << reverse_y2 * in_image_height << std::endl;
    }
    std::cout << std::endl;


    ///////////////////////////////////////////////////////////
    //                      Tracking                         //
    ///////////////////////////////////////////////////////////
    // if (ret == RETURN_OK && num_face > 0) {
    //     // 임시 변수 선언
    //     int temp_landmark[10];  // 랜드마크 좌표를 임시 저장할 배열
    //     int temp_bbox[4];       // 바운딩 박스 좌표를 임시 저장할 배열

    //     // 트래커 결과를 저장할 배열 할당
    //     int* tracker_bbox = (int*)malloc(num_face * 4 * sizeof(int));
    //     if (!tracker_bbox) {
    //         std::cerr << "Memory allocation failed for tracker_bbox\n";
    //         free(in_image_heap);
    //         free(warped_images);
    //         return -1;
    //     }

    //     ret = fcore_face_tracker(&out_tracks, &out_num_track, bboxes, scores, num_face, 0);
    //     if (ret == RETURN_OK) {
    //         std::cout << "[DEBUG] Tracking successful. Number of tracks: " << out_num_track << std::endl;
            
    //         // 트래커 결과를 tracker_bbox에 복사
    //         for (int a = 0; a < out_num_track; a++) 
    //         {
    //             memcpy(&tracker_bbox[a * 4], out_tracks[a].bbox, sizeof(int) * 4);
    //         }

    //         // 각 얼굴에 대해 트랙 ID 할당
    //         for (int i = 0; i < num_face; i++) {
            
    //             // 가장 유사한 트랙 찾기
    //             int sim_track_id = findMostSimilarBbox(tracker_bbox, num_face, temp_bbox);
                
    //             std::cout << "[DEBUG] Face " << i << " assigned to track " 
    //                         << out_tracks[sim_track_id].track_id 
    //                         << " (score: " << scores[i] << ")" << std::endl;
            
    //         }

    //     } else {
    //         std::cerr << "[ERROR] Face tracking failed" << std::endl;
    //         free(tracker_bbox);
    //         free(in_image_heap);
    //         free(warped_images);
    //         return -1;
    //     }

    //     free(tracker_bbox);
    // }

    ///////////////////////////////////////////////////////////
    //      Draw Bboxes, Scores on image & Image Save        //
    ///////////////////////////////////////////////////////////

    std::cout << "[DEBUG] Creating output directory..." << std::endl;
    // 결과 이미지를 저장할 디렉토리 생성
    char output_folder[1024] = "./sample/draw_bboxes";
    
    // 입력 파일의 전체 상위 경로 가져오기
    std::filesystem::path input_file_path(input_path);
    std::filesystem::path input_dir_path(input_image_dir);
    
    
    // 입력 디렉토리를 기준으로 상대 경로 계산
    std::filesystem::path relative_path;
    try {
        relative_path = std::filesystem::relative(input_file_path, input_dir_path);
    } catch (const std::filesystem::filesystem_error& e) {
        // 상대 경로 계산이 실패하면 파일명만 사용
        relative_path = input_file_path.filename();
    }
    
    std::filesystem::path parent_path = relative_path.parent_path();
    
    // 상위 경로들을 포함한 결과 저장 디렉토리 생성
    std::string result_dir = std::string(output_folder) + "/" + parent_path.string();
    if (mkdir(result_dir.c_str(), 0777) != 0 && errno != EEXIST) {
        std::cerr << "Failed to create directory: " << result_dir << std::endl;
        free(in_image_heap);
        free(warped_images);
        return -1;
    }
        
    char output_path[1024] = {0};
    std::string filename = std::filesystem::path(input_path).filename().string();

    if (filename == "temp_frame.jpg" && frame_number >= 0) {
        std::string video_filename = std::filesystem::path(input_path).parent_path().filename().string();
        if (snprintf(output_path, sizeof(output_path), "%s/result_frame_%04d.jpg", 
                    result_dir.c_str(), frame_number) >= sizeof(output_path)) {
            std::cerr << "Output path too long\n";
            free(in_image_heap);
            free(warped_images);
            return -1;
        }
    } else {
        if (snprintf(output_path, sizeof(output_path), "%s/%s", 
                    result_dir.c_str(), filename.c_str()) >= sizeof(output_path)) {
            std::cerr << "Output path too long\n";
            free(in_image_heap);
            free(warped_images);
            return -1;
        }
    }
    
    // 이제 output_path가 세팅된 후에 txt_path를 만듭니다.
    std::string txt_path = std::string(output_path);
    txt_path = txt_path.substr(0, txt_path.find_last_of('.')) + ".txt";
    std::ofstream out_file(txt_path);
    if (!out_file.is_open()) {
        std::cerr << "Failed to open output text file: " << txt_path << std::endl;
        return -1;
    }

    // 얼굴이 검출되지 않았을 때도 빈 파일 생성
    if (num_face == 0) {
        out_file.close();
        std::cout << "No faces detected. Empty result file created: " << txt_path << std::endl;
    } else {
        // YOLO 형식으로 각 얼굴의 정보 저장
        // 형식: class_id confidence x_center y_center width height
        for (int i = 0; i < num_face; i++) {
            // 바운딩 박스 좌표를 YOLO 형식으로 변환
            float x1 = bboxes[i*4];
            float y1 = bboxes[i*4+1];
            float x2 = bboxes[i*4+2];
            float y2 = bboxes[i*4+3];
        
            // YOLO 형식으로 변환 (상대 좌표)
            float x_center = (x1 + x2) / in_image_width;
            float y_center = (y1 + y2) / in_image_height;
            float width = x2 / in_image_width;
            float height = y2 / in_image_height;
            
            // 파일에 저장 (class_id는 0으로 고정, confidence는 scores[i] 사용)
            out_file << "0 " << std::fixed << std::setprecision(5) 
                     << x_center << " "
                     << y_center << " "
                     << width << " "
                     << height << " "
                     << scores[i] << "\n";
        }
        out_file.close();
        std::cout << "Detection results (YOLO format) saved to: " << txt_path << std::endl;
    }
    

    std::cout << "[DEBUG] Allocating draw_bboxes_image_heap..." << std::endl;
    // 결과 이미지 버퍼 할당
    unsigned char* draw_bboxes_image_heap = (unsigned char*)malloc(in_image_height * in_image_width * in_image_channel * sizeof(unsigned char));
    if (!draw_bboxes_image_heap) {
        std::cerr << "Memory allocation failed for draw_bboxes_image_heap\n";
        free(in_image_heap);
        free(warped_images);
        return -1;
    }

    std::cout << "[DEBUG] Drawing bounding boxes..." << std::endl;
    // 바운딩 박스와 랜드마크 그리기
    // Detection 결과 시각화 
    util_draw_bboxes_landmarks(draw_bboxes_image_heap, in_image_heap, in_image_height, in_image_width, bboxes, scores, landmarks, num_face);
            
    if (ret != 0) {
        std::cerr << "Failed to draw bounding boxes and landmarks\n";
        free(in_image_heap);
        free(draw_bboxes_image_heap);
        free(warped_images);
        return -1;
    }
    // 결과 이미지 저장
    ret = util_imwrite(draw_bboxes_image_heap, in_image_height, in_image_width, output_path);
    if (ret != 0) {
        std::cerr << "Failed to save detection result image\n";
        free(in_image_heap);
        free(draw_bboxes_image_heap);
        free(warped_images);
        return -1;
    }
    

    // // Tracker 결과 시각화
    // if (ret == RETURN_OK && num_face > 0 && out_tracks != NULL) {
    //     // OpenCV Mat으로 변환 (BGR 형식)
    //     cv::Mat result_image(in_image_height, in_image_width, CV_8UC3, draw_bboxes_image_heap);
        
    //     // 원본 이미지 복사
    //     memcpy(draw_bboxes_image_heap, in_image_heap, in_image_height * in_image_width * in_image_channel);
        
    //     // Tracker의 모든 바운딩 박스와 ID 그리기
    //     for (int i = 0; i < out_num_track; i++) {
    //         // Tracker의 바운딩 박스 그리기
    //         cv::rectangle(result_image,
    //             cv::Point(out_tracks[i].bbox[0], out_tracks[i].bbox[1]),
    //             cv::Point(out_tracks[i].bbox[0] + out_tracks[i].bbox[2], 
    //                      out_tracks[i].bbox[1] + out_tracks[i].bbox[3]),
    //             cv::Scalar(0, 255, 0), 2);  // 녹색으로 박스 그리기

    //         // Track ID 텍스트 생성
    //         std::string track_text = "Track: " + std::to_string(out_tracks[i].track_id);
            
    //         // 텍스트 위치 계산 (바운딩 박스 아래에 표시)
    //         cv::Point text_pos(out_tracks[i].bbox[0], 
    //                          out_tracks[i].bbox[1] + out_tracks[i].bbox[3] + 20);
            
    //         // 텍스트 배경을 위한 사각형 그리기
    //         cv::Size text_size = cv::getTextSize(track_text, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, nullptr);
    //         cv::rectangle(result_image, 
    //                     cv::Point(text_pos.x, text_pos.y - text_size.height),
    //                     cv::Point(text_pos.x + text_size.width, text_pos.y + 5),
    //                     cv::Scalar(0, 0, 0), -1);  // 검은색 배경
            
    //         // 텍스트 그리기 (BGR 형식 사용)
    //         cv::putText(result_image, track_text, text_pos,
    //                   cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 255, 0), 1);  // 녹색 텍스트
    //     }
        
    //     // 결과를 다시 draw_bboxes_image_heap에 복사
    //     memcpy(draw_bboxes_image_heap, result_image.data, in_image_height * in_image_width * in_image_channel);
    // }

    // // 결과 이미지 저장
    // ret = util_imwrite(draw_bboxes_image_heap, in_image_height, in_image_width, output_path);
    // if (ret != 0) {
    //     std::cerr << "Failed to save detection result image\n";
    //     free(in_image_heap);
    //     free(draw_bboxes_image_heap);
    //     free(warped_images);
    //     return -1;
    // }

    std::cout << "Detection results saved to: " << output_path << std::endl;

    std::cout << "[DEBUG] Cleaning up memory..." << std::endl;
    // 메모리 해제
    free(in_image_heap);
    free(draw_bboxes_image_heap);
    free(warped_images);
    if (out_tracks != NULL) {
        free(out_tracks);
    }


    std::cout << "[DEBUG] process_frame completed successfully" << std::endl;
    return 0;
}

int main() {
    std::cout << "start" << std::endl;

    // 성능 통계 초기화
    reset_performance_stats();

    int ret = RETURN_FAIL;

    // char *model_path = (char *)"/workspace/home/<USER>/git/qfaceengine_sdk/model";
    char *model_path = (char *)"/workspace/home/<USER>/facedetection/facecore-legacy/qfaceengine_sdk/model";

    std::cout << "[DEBUG] Before fcore_init_multi" << std::endl;
    ret = fcore_init_multi(2, 1);
    std::cout << "[DEBUG] After fcore_init_multi : " << ret << std::endl;

    std::cout << "[DEBUG] Before fcore_setGpuAvailable" << std::endl;
    ret = fcore_setGpuAvailable(false);
    std::cout << "[DEBUG] After fcore_setGpuAvailable : " << ret << std::endl;

    std::cout << "[DEBUG] Before fcore_init_FD_yolo" << std::endl;
    ret = fcore_init_FD_yolo(model_path);
    std::cout << "[DEBUG] After fcore_init_FD_yolo : " << ret << std::endl;

    // // Tracker 설정
    ret = fcore_tracker_config(0.1f, 0.6f, 0.5f, 0);  // tracker_threshold, high_threshold, match_thresh, tracker_num
    std::cout << "fcore_tracker_config : " << ret << std::endl;

    // std::string input_image_dir = "/workspace/raid/RequiredDatasets/QFaceServer/aihub_mask_sample";
    std::string input_image_dir = "/workspace/raid/RequiredDatasets/FD/suprema/imgs/";
    // input_image_dir = "/workspace/home/<USER>/facedetection/facecore-legacy/qfaceengine_sdk/sample/rv1126_testset";
    std::vector<std::string> media_files = get_files_in_directory(input_image_dir);

    int image_count = 0; // Add this before the for loop
    for (const auto& input_path : media_files) {
        std::string extension = get_extension(input_path);
        
        // 1. 이미지 처리
        if (extension == ".jpg" || extension == ".jpeg" || extension == ".png") {
            ret = process_frame(input_path, -1);
            if (ret != 0) {
                std::cerr << "Failed to process image: " << input_path << "\n";
                continue;
            }
            image_count++;
        }
        // 2. 동영상 처리
        else if (extension == ".mp4") {
            std::cout << "[DEBUG] Starting video processing: " << input_path << std::endl;
            
            // 파일 존재 여부 확인
            if (!std::filesystem::exists(input_path)) {
                std::cerr << "[ERROR] Video file does not exist: " << input_path << std::endl;
                continue;
            }
            std::cout << "[DEBUG] Video file exists" << std::endl;

            // 파일 크기 확인
            try {
                auto file_size = std::filesystem::file_size(input_path);
                std::cout << "[DEBUG] Video file size: " << file_size << " bytes" << std::endl;
                if (file_size == 0) {
                    std::cerr << "[ERROR] Video file is empty" << std::endl;
                    continue;
                }
            } catch (const std::filesystem::filesystem_error& e) {
                std::cerr << "[ERROR] Failed to get file size: " << e.what() << std::endl;
                continue;
            }

            std::cout << "[DEBUG] Attempting to open video file..." << std::endl;
            
            cv::VideoCapture cap;
            std::cout << "[DEBUG] VideoCapture object created" << std::endl;
            
            // OpenCV 백엔드 정보 출력
            std::cout << "[DEBUG] Available OpenCV backends:" << std::endl;
            std::cout << "- CAP_ANY: " << cv::CAP_ANY << std::endl;
            std::cout << "- CAP_FFMPEG: " << cv::CAP_FFMPEG << std::endl;
            std::cout << "- CAP_GSTREAMER: " << cv::CAP_GSTREAMER << std::endl;
            
            // 현재 사용 중인 백엔드 확인
            int backend = cap.get(cv::CAP_PROP_BACKEND);
            std::cout << "[DEBUG] Current backend: " << backend << std::endl;
            
            cap.open(input_path);
            std::cout << "[DEBUG] VideoCapture.open() called" << std::endl;
            
            if (!cap.isOpened()) {
                std::cerr << "[ERROR] Failed to open video: " << input_path << "\n";
                std::cerr << "[ERROR] OpenCV error code: " << cap.get(cv::CAP_PROP_BACKEND) << std::endl;
                
                // 추가적인 오류 정보 출력
                std::cerr << "[ERROR] OpenCV version: " << CV_VERSION << std::endl;
                std::cerr << "[ERROR] Build information: " << cv::getBuildInformation() << std::endl;
                
                continue;
            }
            std::cout << "[DEBUG] Video file opened successfully" << std::endl;

            // 동영상 정보 가져오기
            std::cout << "[DEBUG] Reading video properties..." << std::endl;
            int width = static_cast<int>(cap.get(cv::CAP_PROP_FRAME_WIDTH));
            std::cout << "[DEBUG] Width: " << width << std::endl;
            
            int height = static_cast<int>(cap.get(cv::CAP_PROP_FRAME_HEIGHT));
            std::cout << "[DEBUG] Height: " << height << std::endl;
            
            double fps = cap.get(cv::CAP_PROP_FPS);
            std::cout << "[DEBUG] FPS: " << fps << std::endl;
            
            int total_frames = static_cast<int>(cap.get(cv::CAP_PROP_FRAME_COUNT));
            std::cout << "[DEBUG] Total frames: " << total_frames << std::endl;

            if (width <= 0 || height <= 0 || fps <= 0 || total_frames <= 0) {
                std::cerr << "[ERROR] Invalid video properties: " << input_path << "\n";
                std::cerr << "[ERROR] Width: " << width << ", Height: " << height 
                         << ", FPS: " << fps << ", Total frames: " << total_frames << std::endl;
                cap.release();
                continue;
            }

            // 프레임 카운터
            int frame_number = 0;
            cv::Mat frame;
            std::string temp_frame_path = "./temp_frame.jpg";

            try {
                std::cout << "[DEBUG] Starting frame reading loop..." << std::endl;
                while (cap.read(frame)) {
                    std::cout << "[DEBUG] Processing frame " << frame_number 
                              << " (Frame size: " << frame.size() << ")" << std::endl;
                    
                    if (frame.empty()) {
                        std::cerr << "[ERROR] Empty frame detected at frame " << frame_number << std::endl;
                        continue;
                    }

                    // 프레임을 임시 파일로 저장
                    std::cout << "[DEBUG] Saving frame to temporary file..." << std::endl;
                    if (!cv::imwrite(temp_frame_path, frame)) {
                        std::cerr << "[ERROR] Failed to save temporary frame at frame " << frame_number << std::endl;
                        continue;
                    }
                    std::cout << "[DEBUG] Frame saved successfully" << std::endl;

                    // process_frame으로 처리
                    std::cout << "[DEBUG] Processing frame with face detection..." << std::endl;
                    ret = process_frame(temp_frame_path, frame_number);
                    if (ret != 0) {
                        std::cerr << "[ERROR] Failed to process frame " << frame_number << std::endl;
                        // 임시 파일 삭제 시도
                        try {
                            std::filesystem::remove(temp_frame_path);
                        } catch (const std::filesystem::filesystem_error& e) {
                            std::cerr << "[ERROR] Failed to remove temporary file: " << e.what() << std::endl;
                        }
                        continue;
                    }

                    // 임시 파일 삭제
                    std::cout << "[DEBUG] Removing temporary file..." << std::endl;
                    try {
                        std::filesystem::remove(temp_frame_path);
                    } catch (const std::filesystem::filesystem_error& e) {
                        std::cerr << "[ERROR] Failed to remove temporary file: " << e.what() << std::endl;
                    }

                    frame_number++;
                    if (frame_number % 10 == 0) {
                        std::cout << "[DEBUG] Progress: Processed " << frame_number << " frames..." << std::endl;
                    }
                }
                std::cout << "[DEBUG] Frame reading loop completed" << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception during video processing: " << e.what() << std::endl;
            }

            std::cout << "[DEBUG] Releasing video capture..." << std::endl;
            cap.release();
            std::cout << "[DEBUG] Video processing completed: " << frame_number << " frames processed\n";

            if (frame_number > 0) {
                double avg_time = static_cast<double>(total_duration_fd) / frame_number;
                printf("[DEBUG] Video processing completed: %d frames processed\n", frame_number);
                printf("[DEBUG] Average Detection Time over %d frames: %.2f ms\n", frame_number, avg_time);
            }
        }
    }

    // 최종 성능 통계 출력
    print_performance_stats();

    // Tracker 해제
    if (tracker_instance != NULL) {
        fcore_tracker_clear(0);  // tracker_num을 0으로 설정
        std::cout << "Face tracker uninitialized" << std::endl;
    }

    fcore_uninit_FD_yolo();

    return 0;
}
