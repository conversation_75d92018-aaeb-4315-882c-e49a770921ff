#include "Test_Global.h"

void Test_DetectFaceImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE::Image fd_image;
	sdk_instance->ReadImageFile(fd_image, IMG_RGB_PATH);
	
	QFE::FDResult fd_result;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->DetectFaceImage(fd_image, fd_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	std::vector<int64_t> test_times;

	for (int i = 0; i < 10; i++)
	{
		auto detect_st = std::chrono::high_resolution_clock::now();	

		ret = sdk_instance->DetectFaceImage(fd_image, fd_result);
		if (ret == QFE_RET_SUCCESS)
		{
			auto detect_nd = std::chrono::high_resolution_clock::now();
			auto detect_duration = std::chrono::duration_cast<ms>((detect_nd - detect_st)).count();

			if (is_detail_log == true)
			{
				printf("\tScore : %lf\n", fd_result.get_score());
				printf("\tBBox\n");
				printf("\t\tpoint: (%d, %d)\n", fd_result.get_bbox_point().x, fd_result.get_bbox_point().y);
				printf("\t\twidth: %d\n", fd_result.get_bbox_width());
				printf("\t\theight: %d\n", fd_result.get_bbox_height());
				printf("\tLandmark\n");
				printf("\t\tLeft eye: (%d, %d)\n", fd_result.get_left_eye_point().x, fd_result.get_left_eye_point().y);
				printf("\t\tRight eye: (%d, %d)\n", fd_result.get_right_eye_point().x, fd_result.get_right_eye_point().y);
				printf("\t\tNose eye: (%d, %d)\n", fd_result.get_nose_point().x, fd_result.get_nose_point().y);
				printf("\t\tLeft mouth: (%d, %d)\n", fd_result.get_left_mouth_point().x, fd_result.get_left_mouth_point().y);
				printf("\t\tRight mouth: (%d, %d)\n", fd_result.get_right_mouth_point().x, fd_result.get_right_mouth_point().y);
				printf("\tImage Quality ( Luminance Average, Blurness ) : %d, %d\n", fd_result.get_iq().y_avg[0], fd_result.get_iq().blurness); 
				printf("\tWarped Image\n");
				printf("\t\twidth: %d\n", fd_result.get_warped_image().get_image_width());
				printf("\t\theight: %d\n", fd_result.get_warped_image().get_image_height());
				printf("\t\tchannel: %d\n", fd_result.get_warped_image().get_image_channel());
				printf("\t\tlength: %d\n", fd_result.get_warped_image().get_image_length());
			}

			test_times.push_back(detect_duration);
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

    auto min_time = *std::min_element(test_times.begin(), test_times.end());

    printf("\n\t[Detection Time] : %s ms\n", std::to_string(min_time).c_str());
}

void Test_EstimateHeadPoseImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);

	QFE::Image hpe_image;
	sdk_instance->ReadImageFile(hpe_image, IMG_RGB_PATH);

	QFE_HPEResult hpe_result;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->EstimateHeadPoseImage(hpe_image, hpe_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	std::vector<int64_t> test_times;

	for (int i = 0; i < 10; i++)
	{
		auto hpe_st = std::chrono::high_resolution_clock::now();

		ret = sdk_instance->EstimateHeadPoseImage(hpe_image, hpe_result);
		if (ret == QFE_RET_SUCCESS)
		{
			auto hpe_nd = std::chrono::high_resolution_clock::now();
			auto hpe_duration = std::chrono::duration_cast<ms>((hpe_nd - hpe_st)).count();

			if (is_detail_log == true)
			{
				printf("\tyaw : %lf\n", hpe_result.yaw);
				printf("\tpitch : %lf\n", hpe_result.pitch);
				printf("\troll : %lf\n", hpe_result.roll);
			}

			test_times.push_back(hpe_duration);
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

    auto min_time = *std::min_element(test_times.begin(), test_times.end());

    printf("\n\t[Head Pose Estimation time] : %s ms\n", std::to_string(min_time).c_str());
}

void Test_CheckMaskImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);

	QFE::Image ms_image;
	sdk_instance->ReadImageFile(ms_image, MS_TEST_PATH);

	QFE_MSResult ms_result;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->CheckMaskImage(ms_image, ms_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	std::vector<int64_t> test_times;

	for (int i = 0; i < 10; i++)
	{
		auto ms_st = std::chrono::high_resolution_clock::now();

		ret = sdk_instance->CheckMaskImage(ms_image, ms_result);
		if (ret == QFE_RET_SUCCESS)
		{
			auto ms_nd = std::chrono::high_resolution_clock::now();
			auto ms_duration = std::chrono::duration_cast<ms>((ms_nd - ms_st)).count();

			if (is_detail_log == true)
			{
				printf("\tscore: %d\n", ms_result.ms_score);
				printf("\tis_masked: %s\n", ms_result.is_mask ? "YES" : "NO");
			}

			test_times.push_back(ms_duration);
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

    auto min_time = *std::min_element(test_times.begin(), test_times.end());

    printf("\n\t[Mask segmentation time] : %s ms\n", std::to_string(min_time).c_str());
}

void Test_EstimateFaceAttributeImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);

	QFE::Image fam_image;
	sdk_instance->ReadImageFile(fam_image, IMG_RGB_PATH);

	QFE_FAMResult fam_result;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->EstimateFaceAttributeImage(fam_image, fam_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	std::vector<int64_t> test_times;

	for (int i = 0; i < 10; i++)
	{
		auto fam_st = std::chrono::high_resolution_clock::now();

		ret = sdk_instance->EstimateFaceAttributeImage(fam_image, fam_result);
		if (ret == QFE_RET_SUCCESS)
		{
			auto fam_nd = std::chrono::high_resolution_clock::now();
			auto fam_duration = std::chrono::duration_cast<ms>((fam_nd - fam_st)).count();

			if (is_detail_log == true)
			{
				printf("\tage: %d\n", fam_result.age);
				printf("\tgender: %d\n", fam_result.gender);
				printf("\temotion: %d\n", fam_result.emotion);
				printf("\tglass: %d\n", fam_result.glass);
				printf("\trace: %d\n", fam_result.race);
			}

			test_times.push_back(fam_duration);
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);
		}	
	}

    auto min_time = *std::min_element(test_times.begin(), test_times.end());

    printf("\n\t[Face Attribute Estimation Time] : %s ms\n", std::to_string(min_time).c_str());
}

void Test_ExtractTemplateImage()
{
	int ret = RETURN_FAIL;

	print_function_name(__func__);

	QFE::Image ex_image;
	sdk_instance->ReadImageFile(ex_image, IMG_RGB_PATH);

	QFE::Template ex_template;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->ExtractTemplateImage(ex_image, ex_template);	
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	std::vector<int64_t> test_times;

	for (int i = 0; i < 10; i++)
	{
		auto extract_st = std::chrono::high_resolution_clock::now();

		ret = sdk_instance->ExtractTemplateImage(ex_image, ex_template);	
		if (ret == QFE_RET_SUCCESS) 
		{
			auto extract_nd = std::chrono::high_resolution_clock::now();
			auto extract_duration = std::chrono::duration_cast<ms>((extract_nd - extract_st)).count();

			test_times.push_back(extract_duration);
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

    auto min_time = *std::min_element(test_times.begin(), test_times.end());

    printf("\n\t[Extraction Time] : %s ms\n", std::to_string(min_time).c_str());
}

void Test_CheckMaskFDResult()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE::Image fd_image;
	sdk_instance->ReadImageFile(fd_image, IMG_RGB_PATH);
	
	QFE::FDResult fd_result;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->DetectFaceImage(fd_image, fd_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	auto detect_st = std::chrono::high_resolution_clock::now();

	ret = sdk_instance->DetectFaceImage(fd_image, fd_result);
	if (ret != QFE_RET_SUCCESS)
	{
		printf("\tFailed, ret: %d\n", ret);		
		return;
	}	

	QFE_MSResult ms_result;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->CheckMaskFDResult(fd_result, ms_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	std::vector<int64_t> test_times;

	for (int i = 0; i < 10; i++)
	{
		auto ms_st = std::chrono::high_resolution_clock::now();

		ret = sdk_instance->CheckMaskFDResult(fd_result, ms_result);
		if (ret == QFE_RET_SUCCESS)
		{
			auto ms_nd = std::chrono::high_resolution_clock::now();
			auto ms_duration = std::chrono::duration_cast<ms>((ms_nd - ms_st)).count();

			if (is_detail_log == true)
			{
				printf("\tscore: %d\n", ms_result.ms_score);
				printf("\tis_masked: %s\n", ms_result.is_mask ? "YES" : "NO");
			}
			
			test_times.push_back(ms_duration);
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);		
			return;
		}
	}

    auto min_time = *std::min_element(test_times.begin(), test_times.end());

    printf("\n\t[Mask segmentation Time] : %s ms\n", std::to_string(min_time).c_str());
}

void Test_EstimateFaceAttributeFDResult()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE::Image fd_image;
	sdk_instance->ReadImageFile(fd_image, IMG_RGB_PATH);
	
	QFE::FDResult fd_result;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->DetectFaceImage(fd_image, fd_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	auto detect_st = std::chrono::high_resolution_clock::now();

	ret = sdk_instance->DetectFaceImage(fd_image, fd_result);
	if (ret != QFE_RET_SUCCESS)
	{
		printf("\tFailed, ret: %d\n", ret);		
		return;
	}	

	QFE_FAMResult fam_result;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->EstimateFaceAttributeFDResult(fd_result, fam_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	std::vector<int64_t> test_times;

	for (int i = 0; i < 10; i++)
	{
		auto fam_st = std::chrono::high_resolution_clock::now();

		ret = sdk_instance->EstimateFaceAttributeFDResult(fd_result, fam_result);
		if (ret == QFE_RET_SUCCESS)
		{
			auto fam_nd = std::chrono::high_resolution_clock::now();
			auto fam_duration = std::chrono::duration_cast<ms>((fam_nd - fam_st)).count();

			if (is_detail_log == true)
			{
				printf("\n");
				printf("\tage: %d\n", fam_result.age);
				printf("\tgender: %d\n", fam_result.gender);
				printf("\temotion: %d\n", fam_result.emotion);
				printf("\tglass: %d\n", fam_result.glass);
				printf("\trace: %d\n", fam_result.race);
			}
			
			test_times.push_back(fam_duration);
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);		
			return;
		}
	}

    auto min_time = *std::min_element(test_times.begin(), test_times.end());

    printf("\n\t[Face Attribute Estimation time] : %s ms\n", std::to_string(min_time).c_str());
}

void Test_ExtractTemplateFDResult()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE::Image fd_image;
	sdk_instance->ReadImageFile(fd_image, IMG_RGB_PATH);
	
	QFE::FDResult fd_result;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->DetectFaceImage(fd_image, fd_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	auto detect_st = std::chrono::high_resolution_clock::now();

	ret = sdk_instance->DetectFaceImage(fd_image, fd_result);
	if (ret != QFE_RET_SUCCESS)
	{
		printf("\tFailed, ret: %d\n", ret);		
		return;
	}	

	QFE::Template ex_template;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->ExtractTemplateFDResult(fd_result, ex_template);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	std::vector<int64_t> extraction_times;

	for (int i = 0; i < 10; i++)
	{
		auto extract_st = std::chrono::high_resolution_clock::now();

		ret = sdk_instance->ExtractTemplateFDResult(fd_result, ex_template);	
		if (ret == QFE_RET_SUCCESS) 
		{
			auto extract_nd = std::chrono::high_resolution_clock::now();
			auto extract_duration = std::chrono::duration_cast<ms>((extract_nd - extract_st)).count();

			extraction_times.push_back(extract_duration);
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);		
		}
	}

    auto min_time = *std::min_element(extraction_times.begin(), extraction_times.end());

    printf("\n\t[Extraction Time] : %s ms\n", std::to_string(min_time).c_str());

	return;
}

void Test_DetectMultiFaceImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE::Image fd_image;
	sdk_instance->ReadImageFile(fd_image, MULTI_FACE_TEST_PATH);
	
	std::vector<QFE::FDResult*> fd_result_array;

	for (int i = 0; i < 10; i++)
	{
		ret = sdk_instance->DetectMultiFaceImage(fd_image, fd_result_array);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}

		fd_result_array.clear();
	}

	std::vector<int64_t> test_times;

	sdk_instance->SetSystemParameter(MAX_FACE, 10);

	for (int i = 0; i < 10; i++)
	{
		auto detect_st = std::chrono::high_resolution_clock::now();

		ret = sdk_instance->DetectMultiFaceImage(fd_image, fd_result_array);
		if (ret == QFE_RET_SUCCESS)
		{
			auto detect_nd = std::chrono::high_resolution_clock::now();
			auto detect_duration = std::chrono::duration_cast<ms>((detect_nd - detect_st)).count();
			
			if (is_detail_log == true)
			{
				for (int i = 0; i < fd_result_array.size(); i++) 
				{
					QFE::FDResult *fd_result = fd_result_array[i];
					
					printf("\tFace number: %d\n", i+1);
					printf("\t\tBBox\n");
					printf("\t\t\tpoint: (%d, %d)\n", fd_result->get_bbox_point().x, fd_result->get_bbox_point().y);
					printf("\t\t\twidth: %d\n", fd_result->get_bbox_width());
					printf("\t\t\theight: %d\n", fd_result->get_bbox_height());
					printf("\t\tLandmark\n");
					printf("\t\t\tLeft eye: (%d, %d)\n", fd_result->get_left_eye_point().x, fd_result->get_left_eye_point().y);
					printf("\t\t\tRight eye: (%d, %d)\n", fd_result->get_right_eye_point().x, fd_result->get_right_eye_point().y);
					printf("\t\t\tNose eye: (%d, %d)\n", fd_result->get_nose_point().x, fd_result->get_nose_point().y);
					printf("\t\t\tLeft mouth: (%d, %d)\n", fd_result->get_left_mouth_point().x, fd_result->get_left_mouth_point().y);
					printf("\t\t\tRight mouth: (%d, %d)\n", fd_result->get_right_mouth_point().x, fd_result->get_right_mouth_point().y);
					printf("\t\tImage Quality ( Luminance Average, Blurness ) : %d, %d\n", fd_result->get_iq().y_avg[0], fd_result->get_iq().blurness); 
					printf("\t\tWarped Image\n");
					printf("\t\t\twidth: %d\n", fd_result->get_warped_image().get_image_width());
					printf("\t\t\theight: %d\n", fd_result->get_warped_image().get_image_height());
					printf("\t\t\tchannel: %d\n", fd_result->get_warped_image().get_image_channel());
					printf("\t\t\tlength: %d\n", fd_result->get_warped_image().get_image_length());		
				}
			}

			test_times.push_back(detect_duration);
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);
		}	
	}

    auto min_time = *std::min_element(test_times.begin(), test_times.end());

    printf("\n\t[Extraction Time] : %s ms\n", std::to_string(min_time).c_str());

	fd_result_array.clear();

	return;
}