#include "core_api.h"

#include <iostream>
#include <vector>
#include <chrono>
#include <sys/stat.h>  // for mkdir
#include <sys/types.h> // for mkdir
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
#include <filesystem>
#include <algorithm>   // for transform
#include <fstream>
#include <iomanip>

#define MAX_FACE 10
typedef std::chrono::milliseconds ms;
using namespace std::chrono;

// 전역 변수로 처리 시간 저장
long long total_duration_fd = 0;

// 결과 이미지를 저장할 디렉토리 생성 함수
void create_output_directory() {
    mkdir("draw_bboxes", 0777);
}

// 파일 확장자 가져오기
std::string get_extension(const std::string& filename) {
    size_t pos = filename.find_last_of(".");
    if (pos == std::string::npos) return "";
    std::string ext = filename.substr(pos);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    return ext;
}

// 디렉토리 내 파일 목록 가져오기 (하위 폴더 포함)
std::vector<std::string> get_files_in_directory(const std::string& dir_path) {
    std::vector<std::string> files;
    try {
        for (const auto& entry : std::filesystem::recursive_directory_iterator(dir_path)) {
            if (entry.is_regular_file()) {
                std::string extension = entry.path().extension().string();
                std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
                if (extension == ".jpg" || extension == ".jpeg" || extension == ".png" || extension == ".mp4") {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "Error accessing directory " << dir_path << ": " << e.what() << std::endl;
    }
    
    // 파일 경로 정렬
    std::sort(files.begin(), files.end());
    
    std::cout << "Found " << files.size() << " media files in " << dir_path << " and its subdirectories" << std::endl;
    return files;
}

// 프레임 처리 함수 (이미지/동영상 공용)
int process_frame(const std::string& input_path, int frame_number = -1) {
    std::cout << "[DEBUG] Starting process_frame for: " << input_path << std::endl;

    // 이미지 로드
    unsigned char *in_image_heap = NULL;
    int in_image_height = 0, in_image_width = 0, in_image_channel = 0;
    std::cout << "[DEBUG] Calling util_imread..." << std::endl;
    int ret = util_imread(&in_image_heap, &in_image_height, &in_image_width, &in_image_channel, (char*)input_path.c_str());
    std::cout << "[DEBUG] util_imread returned: " << ret << std::endl;
    std::cout << "[DEBUG] Image properties - Height: " << in_image_height 
              << ", Width: " << in_image_width 
              << ", Channels: " << in_image_channel << std::endl;

    if (ret != 0) {
        std::cerr << "Failed to read image: " << input_path << std::endl;
        return -1;
    }

    if (in_image_heap == NULL || in_image_height <= 0 || in_image_width <= 0 || in_image_channel <= 0) {
        std::cerr << "Invalid image properties: " << input_path << std::endl;
        if (in_image_heap) free(in_image_heap);
        return -1;
    }

    // initialize output variables according to number of "MAX_FACE"
    int landmarks[MAX_FACE * 10] = {0};
    int bboxes[MAX_FACE * 4] = {0};
    float scores[MAX_FACE] = {0.0};
    int num_face = 0;
    struct IQ iqs[MAX_FACE] = {0};

    std::cout << "[DEBUG] Allocating warped_images..." << std::endl;
    unsigned char **warped_images = (unsigned char **)malloc(MAX_FACE * sizeof(unsigned char *));
    if (!warped_images) {
        std::cerr << "Memory allocation failed for warped_images\n";
        free(in_image_heap);
        return -1;
    }

    // warped_images 배열 초기화
    for (int i = 0; i < MAX_FACE; i++) {
        warped_images[i] = NULL;
    }

    ///////////////////////////////////////////////////////////
    //                      Detection                        //
    ///////////////////////////////////////////////////////////

    std::cout << "[DEBUG] Starting face detection..." << std::endl;
    auto detection_start = high_resolution_clock::now();

    ret = fcore_detect_yolo(
        warped_images, &num_face, scores, landmarks, bboxes,
        iqs, in_image_heap, in_image_height, in_image_width, MAX_FACE);

    std::cout << "[DEBUG] fcore_detect_yolo returned: " << ret << std::endl;
    std::cout << "[DEBUG] Number of faces detected: " << num_face << std::endl;

    auto detection_end = high_resolution_clock::now();
    auto duration_fd = duration_cast<milliseconds>(detection_end - detection_start).count();
    total_duration_fd += duration_fd;
    printf("Detected %d face. Time: %ld ms\n", num_face, duration_fd);

    ///////////////////////////////////////////////////////////
    //      Draw Bboxes, Scores on image & Image Save        //
    ///////////////////////////////////////////////////////////
    
    std::cout << "[DEBUG] Creating output directory..." << std::endl;
    // 결과 이미지를 저장할 디렉토리 생성
    char output_folder[1024] = "./sample/draw_bboxes";
    
    // 입력 파일의 상위 폴더명 가져오기
    std::string parent_folder = std::filesystem::path(input_path).parent_path().filename().string();
    if (parent_folder.empty()) {
        parent_folder = "root";  // 최상위 폴더인 경우
    }
    
    // 상위 폴더명으로 결과 저장 디렉토리 생성
    std::string result_dir = std::string(output_folder) + "/" + parent_folder;
    if (mkdir(result_dir.c_str(), 0777) != 0 && errno != EEXIST) {
        std::cerr << "Failed to create directory: " << result_dir << std::endl;
        free(in_image_heap);
        free(warped_images);
        return -1;
    }

    std::cout << "[DEBUG] Allocating draw_bboxes_image_heap..." << std::endl;
    // 결과 이미지 버퍼 할당
    unsigned char* draw_bboxes_image_heap = (unsigned char*)malloc(in_image_height * in_image_width * in_image_channel * sizeof(unsigned char));
    if (!draw_bboxes_image_heap) {
        std::cerr << "Memory allocation failed for draw_bboxes_image_heap\n";
        free(in_image_heap);
        free(warped_images);
        return -1;
    }

    std::cout << "[DEBUG] Drawing bounding boxes..." << std::endl;
    // 바운딩 박스와 랜드마크 그리기
    // ret = util_draw_bboxes(draw_bboxes_image_heap, in_image_heap, in_image_height, in_image_width, 
    //                      bboxes, scores, num_face);
    util_draw_bboxes_landmarks(draw_bboxes_image_heap, in_image_heap, in_image_height, in_image_width, bboxes, scores, landmarks, num_face);
            
    if (ret != 0) {
        std::cerr << "Failed to draw bounding boxes and landmarks\n";
        free(in_image_heap);
        free(draw_bboxes_image_heap);
        free(warped_images);
        return -1;
    }

    std::cout << "[DEBUG] Saving result image..." << std::endl;
    // 결과 이미지 저장
    char output_path[1024] = {0};
    std::string filename = std::filesystem::path(input_path).filename().string();
    
    // 비디오 프레임인 경우 프레임 번호 추가
    if (filename == "temp_frame.jpg" && frame_number >= 0) {
        // 비디오 프레임의 경우 원본 비디오 파일명과 프레임 번호를 사용
        std::string video_filename = std::filesystem::path(input_path).parent_path().filename().string();
        if (snprintf(output_path, sizeof(output_path), "%s/result_frame_%04d.jpg", 
                    result_dir.c_str(), frame_number) >= sizeof(output_path)) {
            std::cerr << "Output path too long\n";
            free(in_image_heap);
            free(draw_bboxes_image_heap);
            free(warped_images);
            return -1;
        }
    } else {
        // 일반 이미지의 경우 기존 방식대로 저장
        if (snprintf(output_path, sizeof(output_path), "%s/%s", 
                    result_dir.c_str(), filename.c_str()) >= sizeof(output_path)) {
            std::cerr << "Output path too long\n";
            free(in_image_heap);
            free(draw_bboxes_image_heap);
            free(warped_images);
            return -1;
        }
    }
    
    ret = util_imwrite(draw_bboxes_image_heap, in_image_height, in_image_width, output_path);
    if (ret != 0) {
        std::cerr << "Failed to save detection result image\n";
        free(in_image_heap);
        free(draw_bboxes_image_heap);
        free(warped_images);
        return -1;
    }
    std::cout << "Detection results saved to: " << output_path << std::endl;

    // 결과 텍스트 파일 저장
    std::string txt_path = std::string(output_path);
    txt_path = txt_path.substr(0, txt_path.find_last_of('.')) + ".txt";
    std::ofstream out_file(txt_path);
    if (!out_file.is_open()) {
        std::cerr << "Failed to open output text file: " << txt_path << std::endl;
        free(in_image_heap);
        free(draw_bboxes_image_heap);
        free(warped_images);
        return -1;
    }

    // YOLO 형식으로 각 얼굴의 정보 저장
    // 형식: class_id confidence x_center y_center width height
    for (int i = 0; i < num_face; i++) {
        // 바운딩 박스 좌표를 YOLO 형식으로 변환
        float x1 = bboxes[i*4];
        float y1 = bboxes[i*4+1];
        float x2 = bboxes[i*4+2];
        float y2 = bboxes[i*4+3];
    

        // YOLO 형식으로 변환 (상대 좌표)
        float x_center = (x1 + x2) / in_image_width;
        float y_center = (y1 + y2) / in_image_height;
        float width = x2 / in_image_width;
        float height = y2 / in_image_height;
        
        // 파일에 저장 (class_id는 0으로 고정, confidence는 scores[i] 사용)
        out_file << "0 " << std::fixed << std::setprecision(5) 
                 << x_center << " "
                 << y_center << " "
                 << width << " "
                 << height << " "
                 << scores[i] << "\n";
    }
    out_file.close();
    std::cout << "Detection results (YOLO format) saved to: " << txt_path << std::endl;

    std::cout << "[DEBUG] Cleaning up memory..." << std::endl;
    // 메모리 해제
    free(in_image_heap);
    free(draw_bboxes_image_heap);
    free(warped_images);

    std::cout << "[DEBUG] process_frame completed successfully" << std::endl;
    return 0;
}

int main() {
    std::cout << "start" << std::endl;

    int ret = RETURN_FAIL;

    char *model_path = (char *)"/workspace/home/<USER>/git/qfaceengine_sdk/model";

    ret = fcore_init_multi(2, 1);
    std::cout << "fcore_init_multi : " << ret << std::endl;

    ret = fcore_setGpuAvailable(false);
    std::cout << "fcore_setGpuAvailable : " << ret << std::endl;

    // ret = fcore_init_model_all(model_path);
    // std::cout << "fcore_init_FD_all : " << ret << std::endl;

    // int max_face_param = 10;
    // ret = fcore_set_param((char *)"multidetect_MaxFaceNumber", &max_face_param);
    // std::cout << "max_face_param : " << ret << std::endl;

    ret = fcore_init_FD_yolo(model_path);
    std::cout << "fcore_init_FD_yolo : " << ret << std::endl;

    // // original - only 1 image
    // char *img_path = (char *)"./sample/imgs/test1_640_384.jpg";

    // unsigned char *in_image_heap = NULL;
    // int in_image_height = 0, in_image_width = 0, in_image_channel = 0;
    // ret = util_imread(&in_image_heap, &in_image_height, &in_image_width, &in_image_channel, img_path);
    // std::cout << "util_imread : " << ret << std::endl;

    //std::string input_image_dir = "./sample/videos"; // 폴더 경로로 수정
    std::string input_image_dir = "/workspace/raid/RequiredDatasets/QFaceServer/aihub/images";
    std::vector<std::string> media_files = get_files_in_directory(input_image_dir);

    for (const auto& input_path : media_files) {
        std::string extension = get_extension(input_path);
        
        // 1. 이미지 처리
        if (extension == ".jpg" || extension == ".jpeg" || extension == ".png") {
            ret = process_frame(input_path);
            if (ret != 0) {
                std::cerr << "Failed to process image: " << input_path << "\n";
                continue;
            }
        }
        // 2. 동영상 처리
        else if (extension == ".mp4") {
            std::cout << "[DEBUG] Starting video processing: " << input_path << std::endl;
            
            // 파일 존재 여부 확인
            if (!std::filesystem::exists(input_path)) {
                std::cerr << "[ERROR] Video file does not exist: " << input_path << std::endl;
                continue;
            }
            std::cout << "[DEBUG] Video file exists" << std::endl;

            // 파일 크기 확인
            try {
                auto file_size = std::filesystem::file_size(input_path);
                std::cout << "[DEBUG] Video file size: " << file_size << " bytes" << std::endl;
                if (file_size == 0) {
                    std::cerr << "[ERROR] Video file is empty" << std::endl;
                    continue;
                }
            } catch (const std::filesystem::filesystem_error& e) {
                std::cerr << "[ERROR] Failed to get file size: " << e.what() << std::endl;
                continue;
            }

            std::cout << "[DEBUG] Attempting to open video file..." << std::endl;
            
            cv::VideoCapture cap;
            std::cout << "[DEBUG] VideoCapture object created" << std::endl;
            
            // OpenCV 백엔드 정보 출력
            std::cout << "[DEBUG] Available OpenCV backends:" << std::endl;
            std::cout << "- CAP_ANY: " << cv::CAP_ANY << std::endl;
            std::cout << "- CAP_FFMPEG: " << cv::CAP_FFMPEG << std::endl;
            std::cout << "- CAP_GSTREAMER: " << cv::CAP_GSTREAMER << std::endl;
            
            // 현재 사용 중인 백엔드 확인
            int backend = cap.get(cv::CAP_PROP_BACKEND);
            std::cout << "[DEBUG] Current backend: " << backend << std::endl;
            
            cap.open(input_path);
            std::cout << "[DEBUG] VideoCapture.open() called" << std::endl;
            
            if (!cap.isOpened()) {
                std::cerr << "[ERROR] Failed to open video: " << input_path << "\n";
                std::cerr << "[ERROR] OpenCV error code: " << cap.get(cv::CAP_PROP_BACKEND) << std::endl;
                
                // 추가적인 오류 정보 출력
                std::cerr << "[ERROR] OpenCV version: " << CV_VERSION << std::endl;
                std::cerr << "[ERROR] Build information: " << cv::getBuildInformation() << std::endl;
                
                continue;
            }
            std::cout << "[DEBUG] Video file opened successfully" << std::endl;

            // 동영상 정보 가져오기
            std::cout << "[DEBUG] Reading video properties..." << std::endl;
            int width = static_cast<int>(cap.get(cv::CAP_PROP_FRAME_WIDTH));
            std::cout << "[DEBUG] Width: " << width << std::endl;
            
            int height = static_cast<int>(cap.get(cv::CAP_PROP_FRAME_HEIGHT));
            std::cout << "[DEBUG] Height: " << height << std::endl;
            
            double fps = cap.get(cv::CAP_PROP_FPS);
            std::cout << "[DEBUG] FPS: " << fps << std::endl;
            
            int total_frames = static_cast<int>(cap.get(cv::CAP_PROP_FRAME_COUNT));
            std::cout << "[DEBUG] Total frames: " << total_frames << std::endl;

            if (width <= 0 || height <= 0 || fps <= 0 || total_frames <= 0) {
                std::cerr << "[ERROR] Invalid video properties: " << input_path << "\n";
                std::cerr << "[ERROR] Width: " << width << ", Height: " << height 
                         << ", FPS: " << fps << ", Total frames: " << total_frames << std::endl;
                cap.release();
                continue;
            }

            // 프레임 카운터
            int frame_number = 0;
            cv::Mat frame;
            std::string temp_frame_path = "./temp_frame.jpg";

            try {
                std::cout << "[DEBUG] Starting frame reading loop..." << std::endl;
                while (cap.read(frame)) {
                    std::cout << "[DEBUG] Processing frame " << frame_number 
                              << " (Frame size: " << frame.size() << ")" << std::endl;
                    
                    if (frame.empty()) {
                        std::cerr << "[ERROR] Empty frame detected at frame " << frame_number << std::endl;
                        continue;
                    }

                    // 프레임을 임시 파일로 저장
                    std::cout << "[DEBUG] Saving frame to temporary file..." << std::endl;
                    if (!cv::imwrite(temp_frame_path, frame)) {
                        std::cerr << "[ERROR] Failed to save temporary frame at frame " << frame_number << std::endl;
                        continue;
                    }
                    std::cout << "[DEBUG] Frame saved successfully" << std::endl;

                    // process_frame으로 처리
                    std::cout << "[DEBUG] Processing frame with face detection..." << std::endl;
                    ret = process_frame(temp_frame_path, frame_number);
                    if (ret != 0) {
                        std::cerr << "[ERROR] Failed to process frame " << frame_number << std::endl;
                        // 임시 파일 삭제 시도
                        try {
                            std::filesystem::remove(temp_frame_path);
                        } catch (const std::filesystem::filesystem_error& e) {
                            std::cerr << "[ERROR] Failed to remove temporary file: " << e.what() << std::endl;
                        }
                        continue;
                    }

                    // 임시 파일 삭제
                    std::cout << "[DEBUG] Removing temporary file..." << std::endl;
                    try {
                        std::filesystem::remove(temp_frame_path);
                    } catch (const std::filesystem::filesystem_error& e) {
                        std::cerr << "[ERROR] Failed to remove temporary file: " << e.what() << std::endl;
                    }

                    frame_number++;
                    if (frame_number % 10 == 0) {
                        std::cout << "[DEBUG] Progress: Processed " << frame_number << " frames..." << std::endl;
                    }
                }
                std::cout << "[DEBUG] Frame reading loop completed" << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception during video processing: " << e.what() << std::endl;
            }

            std::cout << "[DEBUG] Releasing video capture..." << std::endl;
            cap.release();
            std::cout << "[DEBUG] Video processing completed: " << frame_number << " frames processed\n";

            if (frame_number > 0) {
                double avg_time = static_cast<double>(total_duration_fd) / frame_number;
                printf("[DEBUG] Average Detection Time over %d runs: %.2f ms\n", frame_number, avg_time);
            }
        }
    }

    fcore_uninit_FD_yolo();

    return 0;
}
