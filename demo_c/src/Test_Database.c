#include "Test_Global.h"

void Test_EmptyDatabase()
{
	print_function_name_force(__func__);	

    if (remove(DATABASE_PATH) == 0) {
		if (is_detail_log == true)
		{
        	printf("\tRemove Database: %s\n", DATABASE_PATH);
		}
    } 

	auto database_st = std::chrono::high_resolution_clock::now();	

    QFE_RET_CODE ret = QFE_CreateDatabase(DATABASE_PATH);
    if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tFailed, ret: %d\n", ret);
	}

	if (is_detail_log == true)
	{
    	printf("\tCreate Database: %s\n", DATABASE_PATH);
	}

	ret = QFE_LoadDatabase(DATABASE_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tFailed, ret: %d\n", ret);
	}

	if (is_detail_log == true)
	{
    	printf("\tLoad Database: %s\n", DATABASE_PATH);
	}

	auto database_nd = std::chrono::high_resolution_clock::now();
	auto database_duration = std::chrono::duration_cast<ms>((database_nd - database_st)).count();

	printf("\n\t[Database time] : %s ms\n", std::to_string(database_duration).c_str());
}

//
// Image
//
void Test_EnrollImage(unsigned int _user_id)
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image test_image;
	QFE_ReadImageFile(test_image, IMG_RGB_PATH);

	unsigned int user_id = _user_id;
	QFE_EnrollResult enroll_Result;

	auto enroll_st = std::chrono::high_resolution_clock::now();	

	ret = QFE_EnrollImage(user_id, test_image, enroll_Result);
	if (ret == QFE_RET_SUCCESS)
	{
		auto enroll_nd = std::chrono::high_resolution_clock::now();
		auto enroll_duration = std::chrono::duration_cast<ms>((enroll_nd - enroll_st)).count();

		if (is_detail_log == true)
		{
			printf("\tuser id: %d\n", enroll_Result.enrolled_id);
			printf("\tsub id: %d\n", enroll_Result.enrolled_sub_id);
		}
		printf("\n\t[Enroll time] : %s ms\n", std::to_string(enroll_duration).c_str());

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + enroll_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

void Test_IdentifyImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image test_image;
	QFE_ReadImageFile(test_image, IMG_RGB_PATH);

	QFE_IdentifyResult identify_result;

	auto identify_st = std::chrono::high_resolution_clock::now();

	ret = QFE_IdentifyImage(test_image, identify_result);
	if (ret == QFE_RET_SUCCESS)
	{
		auto identify_nd = std::chrono::high_resolution_clock::now();
		auto identify_duration = std::chrono::duration_cast<ms>((identify_nd - identify_st)).count();

		if (is_detail_log == true)
		{
			printf("\tuser id: %d\n", identify_result.user_id);
			printf("\tsub id: %d\n", identify_result.sub_id);
			printf("\tscore: %d\n", identify_result.score);
		}
		printf("\n\t[Identify time] : %s ms\n", std::to_string(identify_duration).c_str());

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + identify_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

void Test_VerifyImage(unsigned int _user_id)
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image test_image;
	QFE_ReadImageFile(test_image, IMG_RGB_PATH);

	QFE_VerifyResult verify_result;
	unsigned int user_id = _user_id;	

	auto verify_st = std::chrono::high_resolution_clock::now();

	ret = QFE_VerifyImage(user_id, test_image, verify_result);
	if (ret == QFE_RET_SUCCESS)
	{
		auto verify_nd = std::chrono::high_resolution_clock::now();
		auto verify_duration = std::chrono::duration_cast<ms>((verify_nd - verify_st)).count();

		if (is_detail_log == true)
		{
			printf("\tsub id: %d\n", verify_result.sub_id);
			printf("\tscore: %d\n", verify_result.score);
		}
		printf("\n\t[Verify time] : %s ms\n", std::to_string(verify_duration).c_str());

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + verify_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

//
// Template
//
void Test_EnrollTemplate(unsigned int _user_id)
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Template test_template;
	QFE_ReadTemplateFile(test_template, TEMPLATE_RGB_PATH);

	unsigned int user_id = _user_id;
	QFE_EnrollResult enroll_Result;

	auto enroll_st = std::chrono::high_resolution_clock::now();	

	ret = QFE_EnrollTemplate(user_id, test_template, enroll_Result);
	if (ret == QFE_RET_SUCCESS)
	{
		auto enroll_nd = std::chrono::high_resolution_clock::now();
		auto enroll_duration = std::chrono::duration_cast<ms>((enroll_nd - enroll_st)).count();

		if (is_detail_log == true)
		{
			printf("\tuser id: %d\n", enroll_Result.enrolled_id);
			printf("\tsub id: %d\n", enroll_Result.enrolled_sub_id);
		}
		printf("\n\t[Enroll time] : %s ms\n", std::to_string(enroll_duration).c_str());

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + enroll_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

void Test_IdentifyTemplate()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);		

	QFE_Template test_template;
	QFE_ReadTemplateFile(test_template, TEMPLATE_RGB_PATH);

	QFE_IdentifyResult identify_result;

	auto identify_st = std::chrono::high_resolution_clock::now();

	ret = QFE_IdentifyTemplate(test_template, identify_result);
	if (ret == QFE_RET_SUCCESS)
	{
		auto identify_nd = std::chrono::high_resolution_clock::now();
		auto identify_duration = std::chrono::duration_cast<ms>((identify_nd - identify_st)).count();

		if (is_detail_log == true)
		{
			printf("\tuser id: %d\n", identify_result.user_id);
			printf("\tsub id: %d\n", identify_result.sub_id);
			printf("\tscore: %d\n", identify_result.score);
		}
		printf("\n\t[Identify time] : %s ms\n", std::to_string(identify_duration).c_str());

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + identify_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

void Test_VerifyTemplate(unsigned int _user_id)
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Template test_template;
	QFE_ReadTemplateFile(test_template, TEMPLATE_RGB_PATH);

	QFE_VerifyResult verify_result;
	unsigned int user_id = _user_id;	

	auto verify_st = std::chrono::high_resolution_clock::now();

	ret = QFE_VerifyTemplate(user_id, test_template, verify_result);
	if (ret == QFE_RET_SUCCESS)
	{
		auto verify_nd = std::chrono::high_resolution_clock::now();
		auto verify_duration = std::chrono::duration_cast<ms>((verify_nd - verify_st)).count();

		if (is_detail_log == true)
		{
			printf("\tsub id: %d\n", verify_result.sub_id);
			printf("\tscore: %d\n", verify_result.score);
		}
		printf("\n\t[Verify time] : %s ms\n", std::to_string(verify_duration).c_str());

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + verify_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

//
// FDResult
//
void Test_EnrollFDResult(unsigned int _user_id)
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image test_image;
	QFE_ReadImageFile(test_image, IMG_RGB_PATH);

	QFE_FDResult fd_result;

	ret = QFE_DetectFaceImage(test_image, fd_result);
	if (ret == QFE_RET_SUCCESS)
	{
		unsigned int user_id = _user_id;
		QFE_EnrollResult enroll_Result;

		auto enroll_st = std::chrono::high_resolution_clock::now();	

		ret = QFE_EnrollFDResult(user_id, fd_result, enroll_Result);
		if (ret == QFE_RET_SUCCESS)
		{
			auto enroll_nd = std::chrono::high_resolution_clock::now();
			auto enroll_duration = std::chrono::duration_cast<ms>((enroll_nd - enroll_st)).count();

			if (is_detail_log == true)
			{
				printf("\tuser id: %d\n", enroll_Result.enrolled_id);
				printf("\tsub id: %d\n", enroll_Result.enrolled_sub_id);
			}
			printf("\n\t[Enroll time] : %s ms\n", std::to_string(enroll_duration).c_str());

			if (is_multi_thread == true)
			{
				multi_thread_time = multi_thread_time + enroll_duration;
			}
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

void Test_IdentifyFDResult()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image test_image;
	QFE_ReadImageFile(test_image, IMG_RGB_PATH);

	QFE_FDResult fd_result;

	ret = QFE_DetectFaceImage(test_image, fd_result);
	if (ret == QFE_RET_SUCCESS)
	{
		QFE_IdentifyResult identify_result;

		auto identify_st = std::chrono::high_resolution_clock::now();

		ret = QFE_IdentifyFDResult(fd_result, identify_result);
		if (ret == QFE_RET_SUCCESS)
		{
			auto identify_nd = std::chrono::high_resolution_clock::now();
			auto identify_duration = std::chrono::duration_cast<ms>((identify_nd - identify_st)).count();

			if (is_detail_log == true)
			{
				printf("\tuser id: %d\n", identify_result.user_id);
				printf("\tsub id: %d\n", identify_result.sub_id);
				printf("\tscore: %d\n", identify_result.score);
			}
			printf("\n\t[Identify time] : %s ms\n", std::to_string(identify_duration).c_str());

			if (is_multi_thread == true)
			{
				multi_thread_time = multi_thread_time + identify_duration;
			}
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

void Test_VerifyFDResult(unsigned int _user_id)
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image test_image;
	QFE_ReadImageFile(test_image, IMG_RGB_PATH);

	QFE_FDResult fd_result;

	ret = QFE_DetectFaceImage(test_image, fd_result);
	if (ret == QFE_RET_SUCCESS)
	{
		QFE_VerifyResult verify_result;
		unsigned int user_id = _user_id;	

		auto verify_st = std::chrono::high_resolution_clock::now();

		ret = QFE_VerifyFDResult(user_id, fd_result, verify_result);
		if (ret == QFE_RET_SUCCESS)
		{
			auto verify_nd = std::chrono::high_resolution_clock::now();
			auto verify_duration = std::chrono::duration_cast<ms>((verify_nd - verify_st)).count();

			if (is_detail_log == true)
			{
				printf("\tsub id: %d\n", verify_result.sub_id);
				printf("\tscore: %d\n", verify_result.score);
			}
			printf("\n\t[Verify time] : %s ms\n", std::to_string(verify_duration).c_str());

			if (is_multi_thread == true)
			{
				multi_thread_time = multi_thread_time + verify_duration;
			}
		}
		else
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

//
// Delete
//
void Test_Delete(unsigned int _user_id)
{
	print_function_name(__func__);	

	auto delete_st = std::chrono::high_resolution_clock::now();	

	QFE_RET_CODE ret = QFE_Delete(_user_id);
	if (ret == QFE_RET_SUCCESS)	
	{
		auto delete_nd = std::chrono::high_resolution_clock::now();
		auto delete_duration = std::chrono::duration_cast<ms>((delete_nd - delete_st)).count();

		printf("\n\t[Delete time] : %s ms\n", std::to_string(delete_duration).c_str());

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + delete_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

void Test_DeleteAll()
{
	print_function_name(__func__);	

	auto delete_st = std::chrono::high_resolution_clock::now();	

	QFE_RET_CODE ret = QFE_DeleteAll();
	if (ret == QFE_RET_SUCCESS)	
	{
		auto delete_nd = std::chrono::high_resolution_clock::now();
		auto delete_duration = std::chrono::duration_cast<ms>((delete_nd - delete_st)).count();

		printf("\n\t[Delete time] : %s ms\n", std::to_string(delete_duration).c_str());

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + delete_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}