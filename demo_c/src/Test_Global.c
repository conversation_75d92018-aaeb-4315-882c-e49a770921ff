#include "Test_Global.h"

void *sdk_instance = nullptr;

int number_of_mode = 0;
int number_of_model_instances = -1;
int number_of_thread_for_test = 0;
bool is_use_gpu = false;
bool is_detail_log = false;
bool is_multi_thread = false;
long multi_thread_time = 0;

void print_memory_check()
{
#ifdef _WIN32
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);

    if (GlobalMemoryStatusEx(&memInfo)) {
        printf("Free memory: %llu MB\n", memInfo.ullAvailPhys / (1024 * 1024));
    }
#else
    struct sysinfo info;

    if (sysinfo(&info) == 0) {
        printf("Free memory: %ld MB\n", info.freeram / (1024 * 1024));
    }
#endif
}

void print_function_name(const char* _func)
{
	if (is_multi_thread == true)
	{
		return;
	}

	printf("________________________________________________________________________________\n");
	printf("%s()\n", _func);
}

void print_function_name_force(const char* _func)
{
	printf("________________________________________________________________________________\n");
	printf("%s()\n", _func);
}

void print_function_name_load_model_for_thread(const char* _func)
{
	printf("________________________________________________________________________________\n");
	printf("%s()\n", _func);
	printf("\tThread: %d\n", number_of_thread_for_test);
	printf("\tModel Instance: %d\n", number_of_model_instances);
}

void print_function_name_for_thread(const char* _func)
{
	printf("________________________________________________________________________________\n");
	printf("%s() Thread: %d, Model Instance: %d\n", _func, number_of_thread_for_test, number_of_model_instances);
}

void Test_InitSDK()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	if (is_multi_thread == true)
	{
		print_function_name_load_model_for_thread(__func__);
	}
	else
	{
		print_function_name(__func__);	
	}

	int number_of_thread = 0;
	util_get_thread_num(&number_of_thread);

	printf("\tNumber of Available Thread: %d\n", number_of_thread);

	sdk_instance = QFE_CreateInstance();

	auto intSDK_st = std::chrono::high_resolution_clock::now();

	ret = QFE_LoadModel(sdk_instance, MODEL_PATH, number_of_model_instances, is_use_gpu);
	if (ret == QFE_RET_SUCCESS)
	{
		auto intSDK_nd = std::chrono::high_resolution_clock::now();
		auto intSDK_duration = std::chrono::duration_cast<ms>((intSDK_nd - intSDK_st)).count();

		const char* sdk_version = QFE_GetSDKVersionString();
		
		if (is_detail_log == true)
		{
			printf("\tSDK Version: %s\n", sdk_version);
		}

		printf("\n\t[Initialize SDK time] : %s ms\n", std::to_string(intSDK_duration).c_str());		
	}
	else
	{
		printf("\tFailed: %d\n", ret);
		exit(-1);
	}
}