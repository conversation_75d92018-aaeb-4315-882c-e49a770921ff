#include "Test_Algorithm.h"
#include "Test_Database.h"
#include "Test_Matcher.h"

void print_usage(const char *program_name) 
{
    printf("Usage: %s --mode=<number_of_mode> --processor=<cpu|gpu> --multi-thread=<true|false> [--model_instance=<number_of_instances>] [--thread=<number_of_threads>] --detail=<true|false>\n", program_name);
    printf("Options:\n");
    printf("\t-m, --mode=<number_of_mode>\n");
    printf("\t\t1. algorithm\t\t(single-thread): FD, HPE, MS, FAM and FR\n");
    printf("\t\t2. algorithm + database\t(single-thread): FD, HPE, MS, FAM, FR, Enroll, Identify, Verify and Delete\n");
    printf("\t\t3. matcher\t\t(single-thread): Identify\n");
    printf("\t\t4. algorithm\t\t(multi-thread)\n");
    printf("\t\t5. algorithm + database\t(multi-thread)\n");
    printf("\t\t6. matcher\t\t(multi-thread)\n");
    printf("\n");
    printf("\t-p, --processor=<cpu|gpu>\t\tSpecify the processor type (cpu or gpu).\n");
    printf("\t-mt, --multi-thread=<true|false>\tEnable or disable multi-threading.\n");
    printf("\t-d, --detail=<true|false>\t\tEnable detailed logging (true or false).\n");
    printf("\n");
    printf("\t-mi, --model_instance=<number>\tSpecify the number of model instances (required for modes 4-6).\n");
    printf("\t-t, --thread=<number_of_threads>\tSpecify the number of threads for testing (required for modes 4-6).\n");
    printf("\n");    
    printf("\t-h, --help\t\t\t\tShow this help message.\n");
}

void parse_input(int argc, char *argv[])
{
    if (argc < 4) 
    {
        print_usage(argv[0]);
        exit(-1);
    }

    for (int i = 1; i < argc; i++) 
    {
        if (strcmp(argv[i], "--help") == 0 || strcmp(argv[i], "-h") == 0) 
		{
			print_usage(argv[0]);
			exit(-1);
		}

		if (strcmp(argv[i], "-m") == 0 && i + 1 < argc) 
        {
            number_of_mode = atoi(argv[++i]);
            if (number_of_mode < 1 || number_of_mode > 6) 
            {
                print_usage(argv[0]);
                exit(-1);
            }
        } 
        else if (strcmp(argv[i], "-p") == 0 && i + 1 < argc) 
        {
            char *processor_type = argv[++i];
            if (strcmp(processor_type, "cpu") == 0) 
            {
                is_use_gpu = false;
            } 
            else if (strcmp(processor_type, "gpu") == 0) 
            {
                is_use_gpu = true;
            } 
            else 
            {
                print_usage(argv[0]);
                exit(-1);
            }
        } 
        else if (strcmp(argv[i], "-mt") == 0 && i + 1 < argc) 
        {
            if (strcmp(argv[++i], "true") == 0) 
            {
                is_multi_thread = true;
            } 
            else if (strcmp(argv[i], "false") == 0) 
            {
                is_multi_thread = false;
                number_of_model_instances = 1;
            } 
            else 
            {
                print_usage(argv[0]);
                exit(-1);
            }
        }
        else if (strcmp(argv[i], "-mi") == 0 && i + 1 < argc) 
        {
            number_of_model_instances = atoi(argv[++i]);
            if (number_of_model_instances <= 0) 
            {
                print_usage(argv[0]);
                exit(-1);
            }
        } 
        else if (strcmp(argv[i], "-t") == 0 && i + 1 < argc) 
        {
            number_of_thread_for_test = atoi(argv[++i]);
        } 
        else if (strcmp(argv[i], "-d") == 0 && i + 1 < argc) 
        {
            if (strcmp(argv[++i], "true") == 0) 
            {
                is_detail_log = true;
            } 
            else if (strcmp(argv[i], "false") == 0) 
            {
                is_detail_log = false;
            } 
            else 
            {
                print_usage(argv[0]);
                exit(-1);
            }
        }
        if (strncmp(argv[i], "--mode=", 7) == 0) 
        {
            number_of_mode = atoi(argv[i] + 7);
            if (number_of_mode < 1 || number_of_mode > 6) 
            {
                print_usage(argv[0]);
                exit(-1);
            }
        } 
         else if (strncmp(argv[i], "--processor=", 12) == 0) 
        {
            char *processor_type = argv[i] + 12;
            if (strcmp(processor_type, "cpu") == 0) 
            {
                is_use_gpu = false;
            } 
            else if (strcmp(processor_type, "gpu") == 0) 
            {
                is_use_gpu = true;
            } 
            else 
            {
                print_usage(argv[0]);
                exit(-1);
            }
        } 
        else if (strncmp(argv[i], "--multi-thread=", 15) == 0) 
        {
            if (strcmp(argv[i] + 15, "true") == 0) 
            {
                is_multi_thread = true;
            } 
            else if (strcmp(argv[i] + 15, "false") == 0) 
            {
                is_multi_thread = false;
				number_of_model_instances = 1;
            } 
            else 
            {
                print_usage(argv[0]);
                exit(-1);
            }
        }
        else if (strncmp(argv[i], "--model_instance=", 16) == 0) 
        {
            number_of_model_instances = atoi(argv[i] + 16);
            if (number_of_model_instances <= 0) 
            {
                print_usage(argv[0]);
                exit(-1);
            }
        } 
        else if (strncmp(argv[i], "--thread=", 9) == 0) 
        {
            number_of_thread_for_test = atoi(argv[i] + 9);
        } 
        else if (strncmp(argv[i], "--detail=", 9) == 0) 
        {
            if (strcmp(argv[i] + 9, "true") == 0) 
            {
                is_detail_log = true;
            } 
            else if (strcmp(argv[i] + 9, "false") == 0) 
            {
                is_detail_log = false;
            } 
            else 
            {
                print_usage(argv[0]);
                exit(-1);
            }
        }
    }

    if (is_multi_thread) 
    {
        if (number_of_model_instances < 1) 
        {
            printf("Error: --model_instance option must be specified when multi-threading is enabled.\n");
            print_usage(argv[0]);
            exit(-1);
        }
        if (number_of_thread_for_test < 1) 
        {
            printf("Error: --thread option must be specified when multi-threading is enabled.\n");
            print_usage(argv[0]);
            exit(-1);
        }
    } 
    else 
    {
        if (number_of_model_instances > 1 || number_of_thread_for_test > 1) 
        {
            printf("Error: --model_instance and --thread options cannot be specified when multi-threading is disabled.\n");
            print_usage(argv[0]);
            exit(-1);
        }
    }
}

void Test_Thread(void (*func)(), const char* func_name)
{
    print_function_name_for_thread(func_name);

    std::vector<std::thread> threads;

    multi_thread_time = 0;

    for (int i = 0; i < number_of_thread_for_test; i++) 
    {
        threads.emplace_back(func);
    }

    for (auto& t : threads) 
    {
        t.join();
    }

    printf("\n[Thread time] : %ld ms\n", multi_thread_time / number_of_thread_for_test);
}

void Test_Thread_with_Param(void (*_func)(unsigned int), const char* func_name)
{
    print_function_name_for_thread(func_name);

    std::vector<std::thread> threads;

    multi_thread_time = 0;

    for (int i = 0; i < number_of_thread_for_test; i++) 
    {
        auto func = std::bind(_func, i+1);
        threads.emplace_back(func);
    }

    for (auto& t : threads) 
    {
        t.join();
    }

    printf("\n[Thread time] : %ld ms\n", multi_thread_time / number_of_thread_for_test);
}

int main(int argc, char *argv[]) 
{
	parse_input(argc, argv);

	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	system("clear");

	printf("===============================  TEST SETTING   ================================\n");	
	printf("Mode: %d\n", number_of_mode);
	printf("Processor: %s\n", is_use_gpu ? "GPU" : "CPU");
	printf("Multi-Thread: %s\n", is_multi_thread ? "YES" : "NO");	
	if (is_multi_thread == true)
	{
		printf("\tModel Instance for Test: %d\n", number_of_model_instances);
		printf("\tNumber of thread for Test: %d\n", number_of_thread_for_test);
	}
	printf("Detail Log: %s\n", is_detail_log ? "YES" : "NO");
	printf("================================================================================\n\n\n");

	Test_InitSDK();

	if (number_of_mode == 1)
	{
		Test_DetectFaceImage();
		Test_EstimateHeadPoseImage();
		Test_CheckMaskImage();
		Test_EstimateFaceAttributeImage();
		Test_ExtractTemplateImage();
        Test_CheckMaskFDResult();
        Test_EstimateFaceAttributeFDResult();
        Test_ExtractTemplateFDResult();
        Test_DetectMultiFaceImage();
	}
	else if (number_of_mode == 2)
	{
        Test_DetectFaceImage();
		Test_EstimateHeadPoseImage();
		Test_CheckMaskImage();
		Test_EstimateFaceAttributeImage();
		Test_ExtractTemplateImage();
		Test_CheckMaskFDResult();
        Test_EstimateFaceAttributeFDResult();
        Test_ExtractTemplateFDResult();
        Test_DetectMultiFaceImage();

		Test_EmptyDatabase();

        Test_EnrollImage(1);
        Test_IdentifyImage();
        Test_VerifyImage(1);

        Test_Delete(1);

        Test_EnrollTemplate(2);
        Test_IdentifyTemplate();
        Test_VerifyTemplate(2);

        Test_DeleteAll();

        Test_EnrollFDResult(3);
        Test_IdentifyFDResult();
        Test_VerifyFDResult(3);
	}
    else if (number_of_mode == 3)
    {
        Test_FullDatabase();
        Test_MatchImage();
    }
    else if (number_of_mode == 4)	
    {
        Test_Thread(Test_DetectFaceImage, "Test_DetectFaceImage");
        Test_Thread(Test_EstimateHeadPoseImage, "Test_EstimateHeadPoseImage");
        Test_Thread(Test_CheckMaskImage, "Test_CheckMaskImage");
        Test_Thread(Test_EstimateFaceAttributeImage, "Test_EstimateFaceAttributeImage");
        Test_Thread(Test_ExtractTemplateImage, "Test_ExtractTemplateImage");
        Test_Thread(Test_CheckMaskFDResult, "Test_CheckMaskFDResult");
        Test_Thread(Test_EstimateFaceAttributeFDResult, "Test_EstimateFaceAttributeFDResult");
        Test_Thread(Test_ExtractTemplateFDResult, "Test_ExtractTemplateFDResult");
        Test_Thread(Test_DetectMultiFaceImage, "Test_DetectMultiFaceImage");
    }
    else if (number_of_mode == 5)	
    {
        Test_Thread(Test_DetectFaceImage, "Test_DetectFaceImage");
        Test_Thread(Test_EstimateHeadPoseImage, "Test_EstimateHeadPoseImage");
        Test_Thread(Test_CheckMaskImage, "Test_CheckMaskImage");
        Test_Thread(Test_EstimateFaceAttributeImage, "Test_EstimateFaceAttributeImage");
        Test_Thread(Test_ExtractTemplateImage, "Test_ExtractTemplateImage");
        Test_Thread(Test_CheckMaskFDResult, "Test_CheckMaskFDResult");
        Test_Thread(Test_EstimateFaceAttributeFDResult, "Test_EstimateFaceAttributeFDResult");
        Test_Thread(Test_ExtractTemplateFDResult, "Test_ExtractTemplateFDResult");
        Test_Thread(Test_DetectMultiFaceImage, "Test_DetectMultiFaceImage");

        Test_EmptyDatabase();

        Test_Thread_with_Param(Test_EnrollImage, "Test_EnrollImage");
        Test_Thread(Test_IdentifyImage, "Test_IdentifyImage");
        Test_Thread_with_Param(Test_VerifyImage, "Test_VerifyImage");

        Test_Thread_with_Param(Test_Delete, "Test_Delete");

        Test_Thread_with_Param(Test_EnrollTemplate, "Test_EnrollTemplate");
        Test_Thread(Test_IdentifyTemplate, "Test_IdentifyTemplate");
        Test_Thread_with_Param(Test_VerifyTemplate, "Test_VerifyTemplate");

        Test_Thread(Test_DeleteAll, "Test_DeleteAll");

        Test_Thread_with_Param(Test_EnrollFDResult, "Test_EnrollFDResult");
        Test_Thread(Test_IdentifyFDResult, "Test_IdentifyFDResult");
        Test_Thread_with_Param(Test_VerifyFDResult, "Test_VerifyFDResult");
    }
     else if (number_of_mode == 6)
    {
        Test_FullDatabase();
        Test_Thread(Test_MatchImage, "Test_MatchImage");
    }

	return 0;
}
