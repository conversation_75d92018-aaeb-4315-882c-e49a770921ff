#include "Test_Matcher.h"

void Test_FullDatabase()
{
	print_function_name_force(__func__);	

    auto load_st = std::chrono::high_resolution_clock::now();

	QFE_RET_CODE ret = QFE_LoadDatabase(FULL_DATABASE_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tFailed, ret: %d\n", ret);
	}

    auto load_nd = std::chrono::high_resolution_clock::now();
    auto load_duration = std::chrono::duration_cast<ms>((load_nd - load_st)).count();

    printf("\n\t[Database time] : %s ms\n", std::to_string(load_duration).c_str());
}

void Test_MatchImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image test_image;
	QFE_ReadImageFile(test_image, IMG_RGB_PATH);

	QFE_IdentifyResult identify_result;

    for (int i = 0; i < 5; i++)
	{
		ret =  QFE_IdentifyImage(test_image, identify_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	auto identify_st = std::chrono::high_resolution_clock::now();

	ret = QFE_IdentifyImage(test_image, identify_result);
	if (ret == QFE_RET_SUCCESS)
	{
		auto identify_nd = std::chrono::high_resolution_clock::now();
		auto identify_duration = std::chrono::duration_cast<ms>((identify_nd - identify_st)).count();

		printf("\tuser id: %d\n", identify_result.user_id);
		printf("\tsub id: %d\n", identify_result.sub_id);
		printf("\tscore: %d\n", identify_result.score);
		printf("\n\t[Identify time] : %s ms\n", std::to_string(identify_duration).c_str());

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + identify_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}