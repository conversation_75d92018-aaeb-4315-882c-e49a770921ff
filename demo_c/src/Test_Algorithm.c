#include "Test_Global.h"

void Test_DetectFaceImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE_Image fd_image;
	QFE_ReadImageFile(sdk_instance, &fd_image, IMG_RGB_PATH);
	
	QFE_FDResult fd_result;

	for (int i = 0; i < 10; i++)
	{
		ret = QFE_DetectFaceImage(sdk_instance, &fd_image, &fd_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	struct timeval detect_st, detect_nd;

	gettimeofday(&detect_st, NULL);

	ret = QFE_DetectFaceImage(fd_image, fd_result);
	if (ret == QFE_RET_SUCCESS)
	{
		gettimeofday(&detect_nd, NULL);
		long detect_duration = (detect_nd.tv_sec - detect_st.tv_sec) * 1000 + (detect_nd.tv_usec - detect_st.tv_usec) / 1000;

		if (is_detail_log == true)
		{
			printf("\tScore : %lf\n", fd_result.score);
			printf("\tBBox\n");
			printf("\t\tpoint: (%d, %d)\n", fd_result.bbox.bbox_point.x, fd_result.bbox.bbox_point.y);
			printf("\t\twidth: %d\n", fd_result.bbox.width);
			printf("\t\theight: %d\n", fd_result.bbox.height);
			printf("\tLandmark\n");
			printf("\t\tLeft eye: (%d, %d)\n", fd_result.landmark.left_eye.x, fd_result.landmark.left_eye.y);
			printf("\t\tRight eye: (%d, %d)\n", fd_result.landmark.right_eye.x, fd_result.landmark.right_eye.y);
			printf("\t\tNose eye: (%d, %d)\n", fd_result.landmark.nose.x, fd_result.landmark.nose.y);
			printf("\t\tLeft mouth: (%d, %d)\n", fd_result.landmark.left_mouth.x, fd_result.landmark.left_mouth.y);
			printf("\t\tRight mouth: (%d, %d)\n", fd_result.get_right_mouth_point().x, fd_result.get_right_mouth_point().y);
			printf("\tImage Quality ( Luminance Average, Blurness ) : %d, %d\n", fd_result.get_iq().y_avg[0], fd_result.get_iq().blurness); 
			printf("\tWarped Image\n");
			printf("\t\twidth: %d\n", fd_result.get_warped_image().get_image_width());
			printf("\t\theight: %d\n", fd_result.get_warped_image().get_image_height());
			printf("\t\tchannel: %d\n", fd_result.get_warped_image().get_image_channel());
			printf("\t\tlength: %d\n", fd_result.get_warped_image().get_image_length());
		}
		printf("\n\t[Detection time] : %ld ms\n", detect_duration);

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + detect_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

void Test_EstimateHeadPoseImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);

	QFE_Image hpe_image;
	QFE_ReadImageFile(sdk_instance, &hpe_image, IMG_RGB_PATH);

	QFE_HPEResult hpe_result;

	for (int i = 0; i < 10; i++)
	{
		ret = QFE_EstimateHeadPoseImage(sdk_instance, &hpe_image, &hpe_result);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}
	}

	struct timeval hpe_st, hpe_nd;

	gettimeofday(&hpe_st, NULL);

	ret = QFE_EstimateHeadPoseImage(hpe_image, hpe_result);
	if (ret == QFE_RET_SUCCESS)
	{
		gettimeofday(&hpe_nd, NULL);
		long hpe_duration = (hpe_nd.tv_sec - hpe_st.tv_sec) * 1000 + (hpe_nd.tv_usec - hpe_st.tv_usec) / 1000;

		if (is_detail_log == true)
		{
			printf("\tyaw : %lf\n", hpe_result.yaw);
			printf("\tpitch : %lf\n", hpe_result.pitch);
			printf("\troll : %lf\n", hpe_result.roll);
		}
		printf("\n\t[Head Pose Estimation time] : %ld ms\n", hpe_duration);

		if (is_multi_thread == true)
		{
			multi_thread_time = multi_thread_time + hpe_duration;
		}
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}
}

void Test_CheckMaskImage()
{
    QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

    print_function_name(__func__);

    QFE_Image ms_image;
    QFE_ReadImageFile(ms_image, MS_TEST_PATH);

    QFE_MSResult ms_result;

    for (int i = 0; i < 10; i++)
    {
        ret = QFE_CheckMaskImage(ms_image, ms_result);
        if (ret != QFE_RET_SUCCESS)
        {
            printf("\tFailed, ret: %d\n", ret);
        }
    }

    struct timeval ms_st, ms_nd;
    gettimeofday(&ms_st, NULL);

    ret = QFE_CheckMaskImage(ms_image, ms_result);
    if (ret == QFE_RET_SUCCESS)
    {
        gettimeofday(&ms_nd, NULL);
        long ms_duration = (ms_nd.tv_sec - ms_st.tv_sec) * 1000 + (ms_nd.tv_usec - ms_st.tv_usec) / 1000;

        if (is_detail_log == true)
        {
            printf("\tscore: %d\n", ms_result.ms_score);
            printf("\tis_masked: %s\n", ms_result.is_mask ? "YES" : "NO");
        }
        printf("\n\t[Mask segmentation time] : %ld ms\n", ms_duration);

        if (is_multi_thread == true)
        {
            multi_thread_time = multi_thread_time + ms_duration;
        }
    }
    else
    {
        printf("\tFailed, ret: %d\n", ret);
    }
}

void Test_EstimateFaceAttributeImage()
{
    QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

    print_function_name(__func__);

    QFE_Image fam_image;
    QFE_ReadImageFile(fam_image, IMG_RGB_PATH);

    QFE_FAMResult fam_result;

    for (int i = 0; i < 10; i++)
    {
        ret = QFE_EstimateFaceAttributeImage(fam_image, fam_result);
        if (ret != QFE_RET_SUCCESS)
        {
            printf("\tFailed, ret: %d\n", ret);
        }
    }

    struct timeval fam_st, fam_nd;
    gettimeofday(&fam_st, NULL);

    ret = QFE_EstimateFaceAttributeImage(fam_image, fam_result);
    if (ret == QFE_RET_SUCCESS)
    {
        gettimeofday(&fam_nd, NULL);
        long fam_duration = (fam_nd.tv_sec - fam_st.tv_sec) * 1000 + (fam_nd.tv_usec - fam_st.tv_usec) / 1000;

        if (is_detail_log == true)
        {
            printf("\tage: %d\n", fam_result.age);
            printf("\tgender: %d\n", fam_result.gender);
            printf("\temotion: %d\n", fam_result.emotion);
            printf("\tglass: %d\n", fam_result.glass);
            printf("\trace: %d\n", fam_result.race);
        }
        printf("\n\t[Face Attribute Estimation time] : %ld ms\n", fam_duration);

        if (is_multi_thread == true)
        {
            multi_thread_time = multi_thread_time + fam_duration;
        }
    }
    else
    {
        printf("\tFailed, ret: %d\n", ret);
    }
}

void Test_ExtractTemplateImage()
{
    int ret = RETURN_FAIL;

    print_function_name(__func__);

    QFE_Image ex_image;
    QFE_ReadImageFile(ex_image, IMG_RGB_PATH);

    QFE_Template ex_template;

    for (int i = 0; i < 10; i++)
    {
        ret = QFE_ExtractTemplateImage(ex_image, ex_template);    
        if (ret != QFE_RET_SUCCESS)
        {
            printf("\tFailed, ret: %d\n", ret);
        }
    }

    struct timeval extract_st, extract_nd;
    gettimeofday(&extract_st, NULL);

    ret = QFE_ExtractTemplateImage(ex_image, ex_template);    
    if (ret == QFE_RET_SUCCESS) 
    {
        gettimeofday(&extract_nd, NULL);
        long extract_duration = (extract_nd.tv_sec - extract_st.tv_sec) * 1000 + (extract_nd.tv_usec - extract_st.tv_usec) / 1000;

        printf("\n\t[Extraction time] : %ld ms\n", extract_duration);

        if (is_multi_thread == true)
        {
            multi_thread_time = multi_thread_time + extract_duration;
        }
    }
    else
    {
        printf("\tFailed, ret: %d\n", ret);
    }
}

void Test_CheckMaskFDResult()
{
    QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

    print_function_name(__func__);    

    QFE_Image fd_image;
    QFE_ReadImageFile(fd_image, IMG_RGB_PATH);
    
    QFE_FDResult fd_result;

    for (int i = 0; i < 10; i++)
    {
        ret = QFE_DetectFaceImage(fd_image, fd_result);
        if (ret != QFE_RET_SUCCESS)
        {
            printf("\tFailed, ret: %d\n", ret);
        }
    }

    struct timeval detect_st, detect_nd;
    gettimeofday(&detect_st, NULL);

    ret = QFE_DetectFaceImage(fd_image, fd_result);
    if (ret != QFE_RET_SUCCESS)
    {
        printf("\tFailed, ret: %d\n", ret);        
        return;
    }    

    QFE_MSResult ms_result;

    for (int i = 0; i < 10; i++)
    {
        ret = QFE_CheckMaskFDResult(fd_result, ms_result);
        if (ret != QFE_RET_SUCCESS)
        {
            printf("\tFailed, ret: %d\n", ret);
        }
    }

    struct timeval ms_st, ms_nd;
    gettimeofday(&ms_st, NULL);

    ret = QFE_CheckMaskFDResult(fd_result, ms_result);
    if (ret == QFE_RET_SUCCESS)
    {
        gettimeofday(&ms_nd, NULL);
        long ms_duration = (ms_nd.tv_sec - ms_st.tv_sec) * 1000 + (ms_nd.tv_usec - ms_st.tv_usec) / 1000;

        if (is_detail_log == true)
        {
            printf("\tscore: %d\n", ms_result.ms_score);
            printf("\tis_masked: %s\n", ms_result.is_mask ? "YES" : "NO");
        }
        printf("\n\t[Mask segmentation time] : %ld ms\n", ms_duration);

        if (is_multi_thread == true)
        {
            multi_thread_time = multi_thread_time + ms_duration;
        }
    }
    else
    {
        printf("\tFailed, ret: %d\n", ret);        
        return;
    }
}

void Test_EstimateFaceAttributeFDResult()
{
    QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

    print_function_name(__func__);    

    QFE_Image fd_image;
    QFE_ReadImageFile(fd_image, IMG_RGB_PATH);
    
    QFE_FDResult fd_result;

    for (int i = 0; i < 10; i++)
    {
        ret = QFE_DetectFaceImage(fd_image, fd_result);
        if (ret != QFE_RET_SUCCESS)
        {
            printf("\tFailed, ret: %d\n", ret);
        }
    }

    struct timeval detect_st, detect_nd;
    gettimeofday(&detect_st, NULL);

    ret = QFE_DetectFaceImage(fd_image, fd_result);
    if (ret != QFE_RET_SUCCESS)
    {
        printf("\tFailed, ret: %d\n", ret);        
        return;
    }    

    QFE_FAMResult fam_result;

    for (int i = 0; i < 10; i++)
    {
        ret = QFE_EstimateFaceAttributeFDResult(fd_result, fam_result);
        if (ret != QFE_RET_SUCCESS)
        {
            printf("\tFailed, ret: %d\n", ret);
        }
    }

    struct timeval fam_st, fam_nd;
    gettimeofday(&fam_st, NULL);

    ret = QFE_EstimateFaceAttributeFDResult(fd_result, fam_result);
    if (ret == QFE_RET_SUCCESS)
    {
        gettimeofday(&fam_nd, NULL);
        long fam_duration = (fam_nd.tv_sec - fam_st.tv_sec) * 1000 + (fam_nd.tv_usec - fam_st.tv_usec) / 1000;

        if (is_detail_log == true)
        {
            printf("\n");
            printf("\tage: %d\n", fam_result.age);
            printf("\tgender: %d\n", fam_result.gender);
            printf("\temotion: %d\n", fam_result.emotion);
            printf("\tglass: %d\n", fam_result.glass);
            printf("\trace: %d\n", fam_result.race);
        }
        printf("\n\t[Face Attribute Estimation time] : %ld ms\n", fam_duration);

        if (is_multi_thread == true)
        {
            multi_thread_time = multi_thread_time + fam_duration;
        }
    }
    else
    {
        printf("\tFailed, ret: %d\n", ret);        
        return;
    }
}

void Test_ExtractTemplateFDResult()
{
    QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

    print_function_name(__func__);    

    QFE_Image fd_image;
    QFE_ReadImageFile(fd_image, IMG_RGB_PATH);
    
    QFE_FDResult fd_result;

    for (int i = 0; i < 10; i++)
    {
        ret = QFE_DetectFaceImage(fd_image, fd_result);
        if (ret != QFE_RET_SUCCESS)
        {
            printf("\tFailed, ret: %d\n", ret);
        }
    }

    struct timeval detect_st, detect_nd;
    gettimeofday(&detect_st, NULL);

    ret = QFE_DetectFaceImage(fd_image, fd_result);
    if (ret != QFE_RET_SUCCESS)
    {
        printf("\tFailed, ret: %d\n", ret);        
        return;
    }    

    QFE_Template ex_template;

    for (int i = 0; i < 10; i++)
    {
        ret = QFE_ExtractTemplateFDResult(fd_result, ex_template);
        if (ret != QFE_RET_SUCCESS)
        {
            printf("\tFailed, ret: %d\n", ret);
        }
    }

    struct timeval extract_st, extract_nd;
    gettimeofday(&extract_st, NULL);

    ret = QFE_ExtractTemplateFDResult(fd_result, ex_template);    
    if (ret == QFE_RET_SUCCESS) {
        gettimeofday(&extract_nd, NULL);
        long extract_duration = (extract_nd.tv_sec - extract_st.tv_sec) * 1000 + (extract_nd.tv_usec - extract_st.tv_usec) / 1000;

        printf("\n\t[Extraction time] : %ld ms\n", extract_duration);

        if (is_multi_thread == true)
        {
            multi_thread_time = multi_thread_time + extract_duration;
        }
    }
    else
    {
        printf("\tFailed, ret: %d\n", ret);        
    }
}

void Test_DetectMultiFaceImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE_Image fd_image;
	QFE_ReadImageFile(fd_image, MULTI_FACE_TEST_PATH);
	
	std::vector<QFE_FDResult*> fd_result_array;

	for (int i = 0; i < 10; i++)
	{
		ret = QFE_DetectMultiFaceImage(fd_image, fd_result_array);
		if (ret != QFE_RET_SUCCESS)
		{
			printf("\tFailed, ret: %d\n", ret);
		}

		fd_result_array.clear();
	}

	struct timeval dtect_st, detect_nd;
    gettimeofday(&dtect_st, NULL);

	ret = QFE_DetectMultiFaceImage(fd_image, fd_result_array);
	if (ret == QFE_RET_SUCCESS)
	{
		gettimeofday(&detect_nd, NULL);
		long detect_duration = (detect_nd.tv_sec - dtect_st.tv_sec) * 1000 + (detect_nd.tv_usec - dtect_st.tv_usec) / 1000;
		
		if (is_detail_log == true)
		{
			for (int i = 0; i < fd_result_array.size(); i++) 
			{
				QFE_FDResult *fd_result = fd_result_array[i];
				
				printf("\tFace number: %d\n", i+1);
				printf("\t\tBBox\n");
				printf("\t\t\tpoint: (%d, %d)\n", fd_result->get_bbox_point().x, fd_result->get_bbox_point().y);
				printf("\t\t\twidth: %d\n", fd_result->get_bbox_width());
				printf("\t\t\theight: %d\n", fd_result->get_bbox_height());
				printf("\t\tLandmark\n");
				printf("\t\t\tLeft eye: (%d, %d)\n", fd_result->get_left_eye_point().x, fd_result->get_left_eye_point().y);
				printf("\t\t\tRight eye: (%d, %d)\n", fd_result->get_right_eye_point().x, fd_result->get_right_eye_point().y);
				printf("\t\t\tNose eye: (%d, %d)\n", fd_result->get_nose_point().x, fd_result->get_nose_point().y);
				printf("\t\t\tLeft mouth: (%d, %d)\n", fd_result->get_left_mouth_point().x, fd_result->get_left_mouth_point().y);
				printf("\t\t\tRight mouth: (%d, %d)\n", fd_result->get_right_mouth_point().x, fd_result->get_right_mouth_point().y);
				printf("\t\tImage Quality ( Luminance Average, Blurness ) : %d, %d\n", fd_result->get_iq().y_avg[0], fd_result->get_iq().blurness); 
				printf("\t\tWarped Image\n");
				printf("\t\t\twidth: %d\n", fd_result->get_warped_image().get_image_width());
				printf("\t\t\theight: %d\n", fd_result->get_warped_image().get_image_height());
				printf("\t\t\tchannel: %d\n", fd_result->get_warped_image().get_image_channel());
				printf("\t\t\tlength: %d\n", fd_result->get_warped_image().get_image_length());		
			}
		}

		printf("\n\t[Detection time] : %ld ms\n", detect_duration);

        if (is_multi_thread == true)
        {
            multi_thread_time = multi_thread_time + detect_duration;
        }
	}
	else
	{
		printf("\tFailed, ret: %d\n", ret);
	}	

	return;

	fd_result_array.clear();
}