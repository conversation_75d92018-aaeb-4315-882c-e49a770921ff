#ifndef __TEST_DATABASE_H__
#define __TEST_DATABASE_H__

#include "Test_Global.h"

void Test_EmptyDatabase();

// Image
void Test_EnrollImage(unsigned int _user_id);
void Test_IdentifyImage();
void Test_VerifyImage(unsigned int _user_id);

// Template
void Test_EnrollTemplate(unsigned int _user_id);
void Test_IdentifyTemplate();
void Test_VerifyTemplate(unsigned int _user_id);

// FDResult
void Test_EnrollFDResult(unsigned int _user_id);
void Test_IdentifyFDResult();
void Test_VerifyFDResult(unsigned int _user_id);

// Delete
void Test_Delete(unsigned int _user_id);
void Test_DeleteAll();

#endif