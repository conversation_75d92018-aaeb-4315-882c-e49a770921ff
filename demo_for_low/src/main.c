#include <stdbool.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <assert.h>
#include <sys/resource.h>
#include <sys/time.h>

#include "QFE_API.h"

#define MODEL_PATH "/home/<USER>/Downloads/qfaceengine_sdk/model"
#define LICENSE_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/license/linux_license.dat"
#define IMG_RGB_PATH "/workspace/home/<USER>/fcore/FD/000_il000_ig012_ii100_wl000_glass_0_extside_0_50_vs_4.jpg"
//#define IMG_RGB_PATH "/workspace/home/<USER>/fcore/v1.0.0/test/img/test.jpg"
#define IMG_RGB_PATH2 "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/test_rgb_image/iu_test.jpg"
#define IMG_IR_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/test_ir_image/test_ir_image.jpg"
#define WRAP_IMG_RGB_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/test_rgb_image/test_rgb_wrap_image.jpg"
#define WRAP_IMG_IR_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/test_ir_image/test_ir_wrap_image.jpg"
#define MS_RGB_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/test_rgb_image/test_rgb_mask_segmentation.jpg"
#define MS_IR_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/test_ir_image/test_ir_mask_segmentation.jpg"
#define TEMPLATE_RGB_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/test_rgb_image/test_rgb_template.dat"
#define TEMPLATE_IR_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/test_ir_image/test_ir_template.dat"
#define GALLERY_TEMPLATE_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_template"
#define GALLERY_IMAGE_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image"
#define TEST_GALLERY_IMAGE_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_gallery_image"
#define IMG_WRITE_TEST_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test.jpg"
#define MS_TEST_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/ms_test_image.jpg"
#define MULTI_FACE_TEST_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/multi_fd_test_image.jpg"

#define BASE64_IMG_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/image_base64.txt"
#define BASE64_TEMPLATE_PATH "/mnt/c/Users/<USER>/Desktop/QFaceEngine_SDK/qfaceengine_sdk/test_image/template_base64.txt"

#define DATABASE_PATH_1 "/mnt/c/Users/<USER>/Desktop/QFE_sqlite/qfe_database_1.db"
#define DATABASE_PATH_2 "/mnt/c/Users/<USER>/Desktop/QFE_sqlite/qfe_database_2.db"
#define DATABASE_PATH_3 "/mnt/c/Users/<USER>/Desktop/QFE_sqlite/qfe_database_3.db"
#define DATABASE_PATH_4 "/mnt/c/Users/<USER>/Desktop/QFE_sqlite/qfe_database_4.db"
#define DATABASE_PATH_5 "/mnt/c/Users/<USER>/Desktop/QFE_sqlite/qfe_database_5.db"

void *sdk_instance = NULL;

void print_function_name(const char* _func)
{
	printf("___________________________________\n");
	printf("%s()\n", _func);
}

void Test_instance()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);

	ret = QFE_SetLicense(LICENSE_PATH);
	if (ret != QFE_RET_SUCCESS)
	{
		printf("Fail license: %d\n", ret);
		return;
	}

	printf("Max instance: %d\n", QFE_GetMaxInstance());

	sdk_instance = QFE_CreateInstance();
	if (sdk_instance == NULL)
	{
		printf("NULL\n");
	}
	printf("Number of instance: %d\n", QFE_GetNumberOfInstance());

	sdk_instance = QFE_CreateInstance();
	if (sdk_instance == NULL)
	{
		printf("NULL\n");
	}
	printf("Number of instance: %d\n", QFE_GetNumberOfInstance());

	QFE_Image image;
	ret = QFE_ReadImageFile(sdk_instance, &image, IMG_RGB_PATH);
	printf("Read: %d\n", ret);

	sdk_instance = QFE_CreateInstance();
	if (sdk_instance == NULL)
	{
		printf("NULL\n");
	}
	printf("Number of instance: %d\n", QFE_GetNumberOfInstance());

	sdk_instance = QFE_CreateInstance();
	if (sdk_instance == NULL)
	{
		printf("NULL\n");
	}
	printf("Number of instance: %d\n", QFE_GetNumberOfInstance());

	sdk_instance = QFE_CreateInstance();
	if (sdk_instance == NULL)
	{
		printf("NULL\n");
	}
	printf("Number of instance: %d\n", QFE_GetNumberOfInstance());

	sdk_instance = QFE_CreateInstance();
	if (sdk_instance == NULL)
	{
		printf("NULL\n");
	}
	printf("Number of instance: %d\n", QFE_GetNumberOfInstance());

	sdk_instance = QFE_CreateInstance();
	if (sdk_instance == NULL)
	{
		printf("NULL\n");
	}
	printf("Number of instance: %d\n", QFE_GetNumberOfInstance());

	ret = QFE_ReadImageFile(sdk_instance, &image, IMG_RGB_PATH);
	printf("Read: %d\n", ret);
}

void Test_InitSDK()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);

	ret = QFE_SetLicense(LICENSE_PATH);
	if (ret != QFE_RET_SUCCESS)
	{
		printf("Fail license: %d\n", ret);
		return;
	}

	sdk_instance = QFE_CreateInstance();	
	
	struct rusage usage;
	if (getrusage(RUSAGE_SELF, &usage) == 0) {
		printf("Maximum resident set size: %ld MB\n", usage.ru_maxrss/1024);
	} else {
		perror("getrusage");
	}
	ret = QFE_LoadModel(sdk_instance, MODEL_PATH, false);

	if (getrusage(RUSAGE_SELF, &usage) == 0) {
		printf("Maximum resident set size: %ld MB\n", usage.ru_maxrss/1024);
	} else {
		perror("getrusage");
	}
	
	if (ret == QFE_RET_SUCCESS)
	{
		printf("Number of Instance: %d\n\n", QFE_GetNumberOfInstance(sdk_instance));

		const char* sdk_version = QFE_GetSDKVersionString(sdk_instance);
		printf("\tSDK VersionString: %s\n", sdk_version);

		QFE_Version version;
		QFE_GetSDKVersion(sdk_instance, &version);
		printf("\tSDK Version: %d.%d.%d\n", version.major, version.minor, version.patch);
	}
	else
	{
		printf("\tModle load Failed: %d\n", ret);
		return;
	}
}

void Test_Database()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	// ret = QFE_CreateDatabase(sdk_instance, "test.db");
	// if (ret == QFE_RET_SUCCESS)	
	// {
	// 	printf("\tCreate Success, ret: %d\n", ret);
	// }
	// else
	// {
	// 	printf("\ttCreate Failed, ret: %d\n", ret);
	// }

	ret = QFE_LoadDatabase(sdk_instance, "test.db");
	if (ret == QFE_RET_SUCCESS)	
	{
		printf("\tLoad Success, ret: %d\n", ret);
	}
	else
	{
		printf("\tLoad Failed, ret: %d\n", ret);
	}

	printf("\t\t%d, %d\n", QFE_GetNumberOfAllUsers(sdk_instance), QFE_GetNumberOfAllTemplates(sdk_instance));
}

void Test_Image()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE_Image image;
	memset(&image, 0, sizeof(QFE_Image));

	ret = QFE_ReadImageFile(sdk_instance, &image, IMG_RGB_PATH);
	if (ret == QFE_RET_SUCCESS)	
	{
		printf("\twidth: %d\n", image.width);
		printf("\theight: %d\n", image.height);
		printf("\tchannel: %d\n", image.channel);
		printf("\tlength: %d\n", image.length);		
	}
	else
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&image);

		return;
	}

	ret = QFE_WriteImageFile(sdk_instance, &image, "image_test.jpg");
	if (ret == QFE_RET_SUCCESS)	
	{
		printf("\tWrite Image Success, ret: %d\n", ret);
	}
	else
	{
		printf("\tWrite Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&image);

		return;
	}

	QFE_ReleaseImage(&image);
}

void Test_Template()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE_Template sup_template;
	memset(&sup_template, 0, sizeof(QFE_Template));

	ret = QFE_ReadTemplateFile(sdk_instance, &sup_template, TEMPLATE_RGB_PATH);
	if (ret == QFE_RET_SUCCESS)	
	{
		printf("\tlength: %d\n", sup_template.length);		
	}
	else
	{
		printf("\tRead Template Failed, ret: %d\n", ret);
		QFE_ReleaseTemplate(&sup_template);

		return;
	}

	ret = QFE_WriteTemplateFile(sdk_instance, &sup_template, "template_test.dat");
	if (ret == QFE_RET_SUCCESS)	
	{
		printf("\tWrite Template Success, ret: %d\n", ret);
	}
	else
	{
		printf("\tWrite Template Failed, ret: %d\n", ret);
		QFE_ReleaseTemplate(&sup_template);

		return;
	}

	QFE_ReleaseTemplate(&sup_template);
}

void Test_Config()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	ret = QFE_SetSystemParameter(sdk_instance, SECURITY_LEVEL, SECURITY_LEVEL_MORESECURE);
    if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SetSystemParameter Failed, ret: %d\n", ret);
	}

	ret = QFE_SetSystemParameter(sdk_instance, SECURITY_LEVEL, -1);
    if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SetSystemParameter Failed, ret: %d\n", ret);
	}

	ret = QFE_SetSystemParameter(sdk_instance, SECURITY_LEVEL, 4);
    if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SetSystemParameter Failed, ret: %d\n", ret);
	}

	ret = QFE_SetSystemParameter(sdk_instance, MASK_CHECK_LEVEL, MASK_CHECK_LEVEL_STRONG);
    if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SetSystemParameter Failed, ret: %d\n", ret);
	}

	ret = QFE_SetSystemParameter(sdk_instance, MASK_CHECK_LEVEL, -1);
    if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SetSystemParameter Failed, ret: %d\n", ret);
	}

	ret = QFE_SetSystemParameter(sdk_instance, MASK_CHECK_LEVEL, 4);
    if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SetSystemParameter Failed, ret: %d\n", ret);
	}

	ret = QFE_SetSystemParameter(sdk_instance, AUTO_UPDATE_TEMPLATE, 0);
    if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SetSystemParameter Failed, ret: %d\n", ret);
	}

	ret = QFE_SetSystemParameter(sdk_instance, AUTO_UPDATE_TEMPLATE, -1);
    if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SetSystemParameter Failed, ret: %d\n", ret);
	}

	ret = QFE_SetSystemParameter(sdk_instance, AUTO_UPDATE_TEMPLATE, 2);
    if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SetSystemParameter Failed, ret: %d\n", ret);
	}

	ret = QFE_SaveConfig(sdk_instance, "save_config.json");
	if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SaveConfig Failed, ret: %d\n", ret);
	}
	
	QFE_SetDefaultConfig(sdk_instance);

	ret = QFE_SaveConfig(sdk_instance, "save_default_config.json");
	if (ret != QFE_RET_SUCCESS)
	{
		printf("\tQFE_SaveConfig Failed, ret: %d\n", ret);
		return;
	}
}

void Test_DetectFaceImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE_Image fd_image;
	ret = QFE_ReadImageFile(sdk_instance, &fd_image, IMG_RGB_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&fd_image);

		return;
	}
	
	QFE_FDResult fd_result;

	QFE_DetectFaceImage(sdk_instance, &fd_image, &fd_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tScore : %lf\n", fd_result.score);
		printf("\tBBox\n");
		printf("\t\tpoint: (%d, %d)\n", fd_result.bbox.bbox_point.x, fd_result.bbox.bbox_point.y);
		printf("\t\twidth: %d\n", fd_result.bbox.width);
		printf("\t\theight: %d\n", fd_result.bbox.height);
		printf("\tLandmark\n");
		printf("\t\tLeft eye: (%d, %d)\n", fd_result.landmark.left_eye.x, fd_result.landmark.left_eye.y);
		printf("\t\tRight eye: (%d, %d)\n", fd_result.landmark.right_eye.x, fd_result.landmark.right_eye.y);
		printf("\t\tNose: (%d, %d)\n", fd_result.landmark.nose.x, fd_result.landmark.nose.y);
		printf("\t\tLeft mouth: (%d, %d)\n", fd_result.landmark.left_mouth.x, fd_result.landmark.left_mouth.y);
		printf("\t\tRight mouth: (%d, %d)\n", fd_result.landmark.right_mouth.x, fd_result.landmark.right_mouth.y);
		printf("\tImage Quality ( Luminance Average, Blurness ) : %d, %d\n", fd_result.iq.y_avg[0], fd_result.iq.blurness); 
		printf("\tWarped Image\n");
		printf("\t\twidth: %d\n", fd_result.warped_image.width);
		printf("\t\theight: %d\n", fd_result.warped_image.height);
		printf("\t\tchannel: %d\n", fd_result.warped_image.channel);
		printf("\t\tlength: %d\n", fd_result.warped_image.length);

		// QFE_RET_CODE result = QFE_WriteImageFile(sdk_instance, &fd_result.warped_image, "test_warped_image.jpg");
		// if (result == QFE_RET_SUCCESS)	
		// {
		// 	printf("\tWrite Image Success, ret: %d\n", result);
		// }
		// else
		// {
		// 	printf("\tWrite Image Failed, ret: %d\n", result);
		// 	QFE_ReleaseImage(&fd_image);
		// 	QFE_ReleaseFDResult(&fd_result);

		// 	return;
		// }
	}
	else
	{
		printf("\tFailed\n");		
	}

	QFE_ReleaseImage(&fd_image);
	QFE_ReleaseFDResult(&fd_result);
}

void Test_DetectMultiFaceImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE_Image fd_image;
	ret = QFE_ReadImageFile(sdk_instance, &fd_image, MULTI_FACE_TEST_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&fd_image);

		return;
	}
	
	QFE_FDResult *fd_result_array;
	int fd_result_array_size;

	ret = QFE_DetectMultiFaceImage(sdk_instance, &fd_image, &fd_result_array, &fd_result_array_size);
	if (ret == QFE_RET_SUCCESS)
	{
		for (int i = 0; i < fd_result_array_size; i++) 
		{
        	QFE_FDResult *fd_result = &fd_result_array[i];

			printf("\tScore : %lf\n", fd_result->score);
			printf("\tTrack ID : %d\n", fd_result->track_id);
			// printf("\tBBox\n");
			// printf("\t\tpoint: (%d, %d)\n", fd_result->bbox.bbox_point.x, fd_result->bbox.bbox_point.y);
			// printf("\t\twidth: %d\n", fd_result->bbox.width);
			// printf("\t\theight: %d\n", fd_result->bbox.height);
			// printf("\tLandmark\n");
			// printf("\t\tLeft eye: (%d, %d)\n", fd_result->landmark.left_eye.x, fd_result->landmark.left_eye.y);
			// printf("\t\tRight eye: (%d, %d)\n", fd_result->landmark.right_eye.x, fd_result->landmark.right_eye.y);
			// printf("\t\tNose: (%d, %d)\n", fd_result->landmark.nose.x, fd_result->landmark.nose.y);
			// printf("\t\tLeft mouth: (%d, %d)\n", fd_result->landmark.left_mouth.x, fd_result->landmark.left_mouth.y);
			// printf("\t\tRight mouth: (%d, %d)\n", fd_result->landmark.right_mouth.x, fd_result->landmark.right_mouth.y);
			// printf("\tImage Quality ( Luminance Average, Blurness ) : %d, %d\n", fd_result->iq.y_avg[0], fd_result->iq.blurness); 
			// printf("\tWarped Image\n");
			// printf("\t\twidth: %d\n", fd_result->warped_image.width);
			// printf("\t\theight: %d\n", fd_result->warped_image.height);
			// printf("\t\tchannel: %d\n", fd_result->warped_image.channel);
			// printf("\t\tlength: %d\n", fd_result->warped_image.length);

			// char image_name[256];
			// sprintf(image_name, "test_warped_image_%d.jpg", i);

			// QFE_RET_CODE result = QFE_WriteImageFile(sdk_instance, &fd_result->warped_image, image_name);
			// if (result == QFE_RET_SUCCESS)	
			// {
			// 	printf("\tWrite Image Success, ret: %d\n", result);
			// }
			// else
			// {
			// 	printf("\tWrite Image Failed, ret: %d\n", result);
			// 	QFE_ReleaseImage(&fd_image);
			// 	for (int j = 0; j < fd_result_array_size; j++)
			// 	{
			// 		QFE_ReleaseFDResult(&fd_result_array[i]);
			// 	}

			// 	return;
			// }
		}
	}
	else
	{
		printf("\tFailed\n");		
	}

	QFE_ReleaseImage(&fd_image);
	for (int i = 0; i < fd_result_array_size; i++)
	{
		QFE_ReleaseFDResult(&fd_result_array[i]);
	}
}

void Test_EstimateHeadPoseImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);

	QFE_Image hpe_image;
	ret = QFE_ReadImageFile(sdk_instance, &hpe_image, MULTI_FACE_TEST_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&hpe_image);

		return;
	}

	QFE_HPEResult hpe_result;

	ret = QFE_EstimateHeadPoseImage(sdk_instance, &hpe_image, &hpe_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tyaw : %lf\n", hpe_result.yaw);
		printf("\tpitch : %lf\n", hpe_result.pitch);
		printf("\troll : %lf\n", hpe_result.roll);
	}
	else
	{
		printf("\tFailed\n");
	}

	QFE_ReleaseImage(&hpe_image);
}

void Test_CheckMaskImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);

	QFE_Image ms_image;
	ret = QFE_ReadImageFile(sdk_instance, &ms_image, MS_TEST_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&ms_image);

		return;
	}

	QFE_MSResult ms_result;

	ret = QFE_CheckMaskImage(sdk_instance, &ms_image, &ms_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tis mask: %d\n", ms_result.is_mask);
		printf("\tscore: %d\n", ms_result.ms_score);
	}
	else
	{
		printf("\tFailed\n");
	}

	QFE_ReleaseImage(&ms_image);
}

void Test_CheckMaskImage2()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);

	QFE_Image ms_image;
	ret = QFE_ReadImageFile(sdk_instance, &ms_image, IMG_RGB_PATH2);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&ms_image);

		return;
	}

	QFE_MSResult ms_result;

	ret = QFE_CheckMaskImage(sdk_instance, &ms_image, &ms_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tis mask: %d\n", ms_result.is_mask);
		printf("\tscore: %d\n", ms_result.ms_score);
	}
	else
	{
		printf("\tFailed\n");
	}

	QFE_ReleaseImage(&ms_image);
}

void Test_EstimateFaceAttributeImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);

	QFE_Image fam_image;
	ret = QFE_ReadImageFile(sdk_instance, &fam_image, IMG_RGB_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&fam_image);

		return;
	}

	QFE_FAMResult fam_result;

	ret = QFE_EstimateFaceAttributeImage(sdk_instance, &fam_image, &fam_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tage: %d\n", fam_result.age);
		printf("\tgender: %d\n", fam_result.gender);
		printf("\temotion: %d\n", fam_result.emotion);
		printf("\tglass: %d\n", fam_result.glass);
		printf("\trace: %d\n", fam_result.race);
	}
	else
	{
		printf("\tFailed\n");
	}

	QFE_ReleaseImage(&fam_image);
}

void Test_ExtractTemplateImage()
{
	int ret = RETURN_FAIL;

	print_function_name(__func__);

	QFE_Image ex_image;
	ret = QFE_ReadImageFile(sdk_instance, &ex_image, IMG_RGB_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&ex_image);

		return;
	}

	QFE_Template ex_template;

	ret = QFE_ExtractTemplateImage(sdk_instance, &ex_image, &ex_template);	
	if (ret != QFE_RET_SUCCESS)
	{
		printf("\tFailed\n");
		QFE_ReleaseImage(&ex_image);
		QFE_ReleaseTemplate(&ex_template);

		return;
	}

	ret = QFE_WriteTemplateFile(sdk_instance, &ex_template, "ex_test.dat");
	if (ret == QFE_RET_SUCCESS)	
	{
		printf("\tWrite Template Success, ret: %d\n", ret);
	}
	else
	{
		printf("\tWrite Template Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&ex_image);
		QFE_ReleaseTemplate(&ex_template);

		return;
	}

	QFE_ReleaseImage(&ex_image);
	QFE_ReleaseTemplate(&ex_template);
}

void Test_useFDResult()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	print_function_name(__func__);	

	QFE_Image fd_image;
	ret = QFE_ReadImageFile(sdk_instance, &fd_image, IMG_RGB_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&fd_image);

		return;
	}

	QFE_FDResult fd_result;

	QFE_DetectFaceImage(sdk_instance, &fd_image, &fd_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tScore : %lf\n", fd_result.score);
		printf("\tBBox\n");
		printf("\t\tpoint: (%d, %d)\n", fd_result.bbox.bbox_point.x, fd_result.bbox.bbox_point.y);
		printf("\t\twidth: %d\n", fd_result.bbox.width);
		printf("\t\theight: %d\n", fd_result.bbox.height);
		printf("\tLandmark\n");
		printf("\t\tLeft eye: (%d, %d)\n", fd_result.landmark.left_eye.x, fd_result.landmark.left_eye.y);
		printf("\t\tRight eye: (%d, %d)\n", fd_result.landmark.right_eye.x, fd_result.landmark.right_eye.y);
		printf("\t\tNose: (%d, %d)\n", fd_result.landmark.nose.x, fd_result.landmark.nose.y);
		printf("\t\tLeft mouth: (%d, %d)\n", fd_result.landmark.left_mouth.x, fd_result.landmark.left_mouth.y);
		printf("\t\tRight mouth: (%d, %d)\n", fd_result.landmark.right_mouth.x, fd_result.landmark.right_mouth.y);
		printf("\tImage Quality ( Luminance Average, Blurness ) : %d, %d\n", fd_result.iq.y_avg[0], fd_result.iq.blurness); 
		printf("\tWarped Image\n");
		printf("\t\twidth: %d\n", fd_result.warped_image.width);
		printf("\t\theight: %d\n", fd_result.warped_image.height);
		printf("\t\tchannel: %d\n", fd_result.warped_image.channel);
		printf("\t\tlength: %d\n", fd_result.warped_image.length);
	}
	else
	{
		printf("\tFailed\n");		
		QFE_ReleaseImage(&fd_image);
		QFE_ReleaseFDResult(&fd_result);

		return;
	}	

	QFE_MSResult ms_result;

	ret = QFE_CheckMaskFDResult(sdk_instance, &fd_result, &ms_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tscore: %d\n", ms_result.ms_score);
	}
	else
	{
		printf("\tFailed\n");
		QFE_ReleaseImage(&fd_image);
		QFE_ReleaseFDResult(&fd_result);

		return;
	}

	QFE_FAMResult fam_result;

	ret = QFE_EstimateFaceAttributeFDResult(sdk_instance, &fd_result, &fam_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tage: %d\n", fam_result.age);
		printf("\tgender: %d\n", fam_result.gender);
		printf("\temotion: %d\n", fam_result.emotion);
		printf("\tglass: %d\n", fam_result.glass);
		printf("\trace: %d\n", fam_result.race);
	}
	else
	{
		printf("\tFailed\n");
		QFE_ReleaseImage(&fd_image);
		QFE_ReleaseFDResult(&fd_result);

		return;
	}

	QFE_Template ex_template;

	ret = QFE_ExtractTemplateFDResult(sdk_instance, &fd_result, &ex_template);	
	if (ret != QFE_RET_SUCCESS)
	{
		printf("\tFailed\n");
		QFE_ReleaseTemplate(&ex_template);
		QFE_ReleaseImage(&fd_image);
		QFE_ReleaseFDResult(&fd_result);

		return;
	}

	ret = QFE_WriteTemplateFile(sdk_instance, &ex_template, "ex_test.dat");
	if (ret == QFE_RET_SUCCESS)	
	{
		printf("\tWrite Template Success, ret: %d\n", ret);
	}
	else
	{
		printf("\tWrite Template Failed, ret: %d\n", ret);
		QFE_ReleaseTemplate(&ex_template);
		QFE_ReleaseImage(&fd_image);
		QFE_ReleaseFDResult(&fd_result);

		return;
	}
}

void Test_EnrollImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image enroll_image;
	ret = QFE_ReadImageFile(sdk_instance, &enroll_image, IMG_RGB_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&enroll_image);

		return;
	}

	unsigned int user_id = 1;
	QFE_EnrollResult enroll_Result;

	ret = QFE_EnrollImage(sdk_instance, user_id, &enroll_image, &enroll_Result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tuser id: %d\n", enroll_Result.enrolled_id);
		printf("\tsub id: %d\n", enroll_Result.enrolled_sub_id);
	}
	else
	{		
		printf("\tFailed\n");
		printf("ret: %d\n", ret);
	}

	QFE_ReleaseImage(&enroll_image);
}

void Test_EnrollImage2()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image enroll_image;
	ret = QFE_ReadImageFile(sdk_instance, &enroll_image, IMG_RGB_PATH2);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&enroll_image);

		return;
	}

	unsigned int user_id = 1;
	QFE_EnrollResult enroll_Result;

	ret = QFE_EnrollImage(sdk_instance, user_id, &enroll_image, &enroll_Result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tuser id: %d\n", enroll_Result.enrolled_id);
		printf("\tsub id: %d\n", enroll_Result.enrolled_sub_id);
	}
	else
	{		
		printf("\tFailed\n");
		printf("ret: %d\n", ret);
	}

	QFE_ReleaseImage(&enroll_image);
}

void Test_IdentifyTemplate()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);		

	QFE_Template identify_template;
	ret = QFE_ReadTemplateFile(sdk_instance, &identify_template, TEMPLATE_RGB_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Template Failed, ret: %d\n", ret);
		QFE_ReleaseTemplate(&identify_template);

		return;
	}

	QFE_IdentifyResult identify_result;

	ret = QFE_IdentifyTemplate(sdk_instance, &identify_template, &identify_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tuser id: %d\n", identify_result.user_id);
		printf("\tsub id: %d\n", identify_result.sub_id);
		printf("\tscore: %d\n", identify_result.score);
	}
	else
	{
		printf("\tFailed\n");
	}

	QFE_ReleaseTemplate(&identify_template);
}

void Test_IdentifyImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image identify_image;
	ret = QFE_ReadImageFile(sdk_instance, &identify_image, IMG_RGB_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&identify_image);

		return;
	}

	QFE_IdentifyResult identify_result;

	ret = QFE_IdentifyImage(sdk_instance, &identify_image, &identify_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tuser id: %d\n", identify_result.user_id);
		printf("\tsub id: %d\n", identify_result.sub_id);
		printf("\tscore: %d\n", identify_result.score);
	}
	else
	{
		printf("\tFailed\n");
	}

	QFE_ReleaseImage(&identify_image);
}

void Test_IdentifyImage2()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image identify_image;
	ret = QFE_ReadImageFile(sdk_instance, &identify_image, IMG_RGB_PATH2);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&identify_image);

		return;
	}

	QFE_IdentifyResult identify_result;

	ret = QFE_IdentifyImage(sdk_instance, &identify_image, &identify_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tuser id: %d\n", identify_result.user_id);
		printf("\tsub id: %d\n", identify_result.sub_id);
		printf("\tscore: %d\n", identify_result.score);
	}
	else
	{
		printf("\tscore: %d\n", identify_result.score);
		printf("\tFailed\n");
		printf("ret: %d\n", ret);
	}

	QFE_ReleaseImage(&identify_image);
}

void Test_IdentifyFDResult()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image identify_image;
	ret = QFE_ReadImageFile(sdk_instance, &identify_image, IMG_RGB_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&identify_image);

		return;
	}

	QFE_FDResult fd_result;

	ret = QFE_DetectFaceImage(sdk_instance, &identify_image, &fd_result);
	if (ret == QFE_RET_SUCCESS)
	{
		QFE_IdentifyResult identify_result;

		ret = QFE_IdentifyFDResult(sdk_instance, &fd_result, &identify_result);
		if (ret == QFE_RET_SUCCESS)
		{
			printf("\tuser id: %d\n", identify_result.user_id);
			printf("\tsub id: %d\n", identify_result.sub_id);
			printf("\tscore: %d\n", identify_result.score);
		}
		else
		{			
			printf("\tFailed\n");
		}
	}
	else
	{
		printf("\tFailed\n");
	}

	QFE_ReleaseImage(&identify_image);
}

void Test_VerifyImage()
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	
	print_function_name(__func__);	

	QFE_Image verify_image;
	ret = QFE_ReadImageFile(sdk_instance, &verify_image, IMG_RGB_PATH);
	if (ret != QFE_RET_SUCCESS)	
	{
		printf("\tRead Image Failed, ret: %d\n", ret);
		QFE_ReleaseImage(&verify_image);

		return;
	}

	QFE_VerifyResult verify_result;
	unsigned int user_id = 1;	

	ret = QFE_VerifyImage(sdk_instance, user_id, &verify_image, &verify_result);
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tsub id: %d\n", verify_result.sub_id);
		printf("\tscore: %d\n", verify_result.score);
	}
	else
	{
		printf("\tFailed\n");
	}

	QFE_ReleaseImage(&verify_image);

}

void Test_Delete()
{
	print_function_name(__func__);	

	QFE_RET_CODE ret = QFE_Delete(sdk_instance, 1);
	if (ret == QFE_RET_SUCCESS)	
	{
		printf("\tDelete Success\n");
	}
	else
	{
		printf("\tDelete Failed\n");
	}
}

void Test_DeleteAll()
{
	print_function_name(__func__);	

	QFE_RET_CODE ret = QFE_DeleteAll(sdk_instance);
	if (ret == QFE_RET_SUCCESS)	
	{
		printf("\tDeleteAll Success\n");
	}
	else
	{
		printf("\tDeleteAll Failed\n");
	}
}

void Test_Image_Base64()
{
	print_function_name(__func__);	

	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	QFE_Image image;
	ret = QFE_ReadImageFileBase64(sdk_instance, &image, "base64_test_image.txt");
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\twidth: %d\n", image.width);
		printf("\theight: %d\n", image.height);
		printf("\tlength: %d\n", image.length);
	}
	else
	{
		printf("\tFAILED\n");
	}

	ret = QFE_WriteImageFileBase64(sdk_instance, &image, "base64_test_image.txt");
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tSUCCESS\n");
	}
	else
	{
		printf("\tFAILED\n");
	}
}

void Test_Template_Base64()
{
	print_function_name(__func__);	

	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	char str[] = "nXV5h4KNhVpyeXyDhoaAhIWngXqIjYt6hXGYi3CUeZV1fnx2enKFhWWGgH+TdnpslW57ko1sdIqHhn1tdIN+Yn2ZjG+FcIt6eXh5eHR0jYmJd4WBeXmNiZJ0eYCGbId4ZX91g2yGhIR4cWt9d3mLaH6LeHtggH+HgoSIdoKWjoVulZJ8nZd0hpBzaYFeZXyLmYx0kWuDeXWTlZCMjYyBhm55cX6QaYd0k2+MioB+W5GGinR4e5OFb2yAgn6DbWSFgHOcg4iVfYx2gYxUh4N5jJd8gHx6h32PpIB8ipCUgoiHhniNhIGHeWljbIB/hYdnc412f4uEbmyCgm9xmmqRe35nbouGeH99dn54hpSMdZ1plIKkfYWAe3uFj4mOknV+iGuOepN1gXp9k2Jzlm9wsXd3i3Zsj3KShZWHkYiEfmdzZotxl4GKfW9/dneAh4t6in5ikXlvjHSZfo5kc4V4g4V+f3yMjXp+eYaJZY+Wh5iIgXl1a5h/dG+FfmGCb4KAYpOCbI2GeIt9h4yJiYaFeo+AiWhchoCFi5J1fIZ9fYWTd4+GZXt0kXWFd16BjnZ/foh5doVtkmiQc4RrmJORfn19eXd7cIx5gHWIfpKKe35keI6DgI1wdmR9hYmMcnB1jY+Mc4J4hHZ/e46DlIOAgXhXjmJ9cY9mfoOHeY95ZnM=";

	QFE_Template template;
	ret = QFE_ReadTemplateFileBase64(sdk_instance, &template, "base64_test_template.txt");
	if (ret == QFE_RET_SUCCESS)
	{
		printf("%d\n", sizeof(str));
		printf("\tlength: %d\n", template.length);
	}
	else
	{
		printf("\tFAILED\n");
	}

	QFE_Template template2;
	ret = QFE_SetTemplateBase64(sdk_instance, &template2, str, sizeof(str));
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tlength: %d\n", template2.length);
	}
	else
	{
		printf("\tFAILED\n");
	}

	ret = QFE_WriteTemplateFileBase64(sdk_instance, &template, "base64_test_template.txt");
	if (ret == QFE_RET_SUCCESS)
	{
		printf("\tSUCCESS\n");
	}
	else
	{
		printf("\tFAILED\n");
	}
}

void Test_FE_Multi()
{
	print_function_name(__func__);	

	fcore_init_multi(3, true);

	int ret = fcore_init_FR_model_type_0(MODEL_PATH);

	unsigned char** fd_image_heaps = (unsigned char**)malloc(sizeof(unsigned char*) * 10);

	int fd_image_height = 0, fd_image_width = 0, fd_image_channel = 0;

	for(int i = 0; i < 10; i++)
	{
		unsigned char* fd_image_heap = NULL;
		util_imread(&fd_image_heap, &fd_image_height, &fd_image_width, &fd_image_channel, IMG_RGB_PATH);

		fd_image_heaps[i] = (unsigned char*)malloc(sizeof(unsigned char) * fd_image_height * fd_image_width * fd_image_channel);
		memcpy(fd_image_heaps[i], fd_image_heap, sizeof(unsigned char) * fd_image_height * fd_image_width * fd_image_channel);

		free(fd_image_heap);
	}

	unsigned char** out_feature_heap = (unsigned char**)malloc(sizeof(unsigned char*) * 10);

	for(int i = 0; i < 10 ; i++)
		out_feature_heap[i] = (unsigned char*)malloc(512 * sizeof(unsigned char));

	int out_feature_len = 0;

	ret = fcore_extract_multi(out_feature_heap, &out_feature_len, fd_image_heaps, fd_image_height, fd_image_width, 0, 10, 3);

	printf("ret : %d\n", ret);

	printf("done\n");
}

void Test_HPE_Multi()
{
	print_function_name(__func__);	

	fcore_init_multi(2, true);

	int ret = fcore_init_HPE(MODEL_PATH);

	unsigned char** in_image_heaps = (unsigned char**)malloc(sizeof(unsigned char*) * 10);

	int in_image_height = 0, in_image_width = 0, in_image_channel = 0;

	for(int i = 0; i < 10; i++)
	{
		unsigned char* in_image_heap = NULL;
		util_imread(&in_image_heap, &in_image_height, &in_image_width, &in_image_channel, IMG_RGB_PATH);

		in_image_heaps[i] = (unsigned char*)malloc(sizeof(unsigned char) * in_image_height * in_image_width * in_image_channel);
		memcpy(in_image_heaps[i], in_image_heap, sizeof(unsigned char) * in_image_height * in_image_width * in_image_channel);

		free(in_image_heap);
	}

	int roi[4] = { 0, };

	roi[0] = 0;
	roi[1] = 0;
	roi[2] = 112;
	roi[3] = 112;

	float **out_pose = (float**)malloc(10 * sizeof(float*));
	
	for(int i = 0; i < 10 ; i++)
		out_pose[i] = (float*)malloc(3 * sizeof(float));

	int out_pose_len = 0;

	ret = fcore_HPE_multi(out_pose, &out_pose_len, in_image_heaps, in_image_height, in_image_width, roi, 10, 2);

	printf("ret : %d\n", ret);

	for(int i = 0; i < 10; i++)
		printf("%lf, %lf, %lf\n", out_pose[i][0], out_pose[i][1], out_pose[i][2]);
	
	printf("done\n");
}

void Test_MS_Multi()
{
	print_function_name(__func__);	

	fcore_init_multi(2, true);

	int ret = fcore_init_MS(MODEL_PATH);

	unsigned char** in_image_heaps = (unsigned char**)malloc(sizeof(unsigned char*) * 10);

	int in_image_height = 0, in_image_width = 0, in_image_channel = 0;

	for(int i = 0; i < 10; i++)
	{
		unsigned char* in_image_heap = NULL;
		util_imread(&in_image_heap, &in_image_height, &in_image_width, &in_image_channel, IMG_RGB_PATH);

		in_image_heaps[i] = (unsigned char*)malloc(sizeof(unsigned char) * in_image_height * in_image_width * in_image_channel);
		memcpy(in_image_heaps[i], in_image_heap, sizeof(unsigned char) * in_image_height * in_image_width * in_image_channel);

		free(in_image_heap);
	}

	unsigned char **out_ms = (unsigned char**)malloc(10 * sizeof(unsigned char*));

	for(int i = 0; i < 10 ; i++)
		out_ms[i] = (unsigned char*)malloc(3 * 100 * 100 * sizeof(unsigned char));

	ret = fcore_MS_multi(out_ms, in_image_heaps, in_image_height, in_image_width, 10, 2);

	printf("ret : %d\n", ret);

	util_imwrite(out_ms[3], 100, 100, "/workspace/home/<USER>/fcore/v1.0.0/test/ms/test.jpg");

	printf("done\n");
}

void Test_FD_ONNX()
{
	int ret = -1;

	print_function_name(__func__);	

	fcore_init();

	fcore_setGpuAvailable(true);

	ret = fcore_init_FD_use_mask(MODEL_PATH);
	// ret = fcore_init_FD(MODEL_PATH);

	printf("status : %d\n", ret);

	const char image_folder_path[1024] = "//media/sup/8088-3FDF/imgs";
	const char fd_folder_path[1024] = "/media/sup/8088-3FDF/FD";

	char** image_list_heap=NULL; 
	int image_num=0;
	ret = util_imlist(&image_list_heap, &image_num, image_folder_path);

	for(int i = 0; i < image_num; i++)
	{
		unsigned char* in_image_heap=NULL;
		int in_image_height = 0, in_image_width = 0, in_image_channel = 0;

		util_imread(&in_image_heap, &in_image_height, & in_image_width, &in_image_channel, image_list_heap[i]);

		unsigned char* warped_image = NULL;
		int warped_image_height=0, warped_image_width=0;
		int landmark[10] = {0,};
		int bbox[4] = {0,};
		float score = 0.0;
		struct IQ iq = {0,};

		printf("image name : %s\n", image_list_heap[i]);

		struct timeval st, nd;
		
		gettimeofday(&st, NULL);

		ret = fcore_detect_add_score(&warped_image, &warped_image_height, &warped_image_width, &score, landmark, bbox, &iq, in_image_heap, in_image_height, in_image_width, 1);

		gettimeofday(&nd, NULL);
		
		long seconds = nd.tv_sec - st.tv_sec;
		long microseconds = nd.tv_usec - st.tv_usec;
		double elapsed_time = seconds * 1000 + microseconds / 1000.0;
		
		printf("time : %.2f ms \n", elapsed_time);

		if(ret == 0)
		{
			printf("Score : %lf\n", score);

			printf("BBOX : %d, %d, %d, %d\n", bbox[0], bbox[1], bbox[2], bbox[3]);

			printf("LANDMARK : %d, %d, %d, %d, %d, %d, %d, %d, %d, %d \n", landmark[0], landmark[1],
				landmark[2], landmark[3], landmark[4], landmark[5], landmark[6], landmark[7], landmark[8], landmark[9]);

			printf("Image Quality ( Luminance Average, Blurness ) : %d, %d\n", iq.y_avg[0], iq.blurness);

			char fd_file_path[1024] = { 0, };
			strcpy(fd_file_path, image_list_heap[i]);
			util_str_replace(fd_file_path, image_folder_path, fd_folder_path);
			util_imwrite(warped_image, warped_image_height, warped_image_width, fd_file_path);

			free(warped_image);
		}

		free(in_image_heap);
	}
}

void Test_HPE_ONNX()
{
	int ret = -1;

	print_function_name(__func__);	

	fcore_init();

	fcore_setGpuAvailable(false);

	ret = fcore_init_FD_use_mask(MODEL_PATH);

	printf("ret : %d\n", ret);

	ret = fcore_init_HPE(MODEL_PATH);

	printf("ret : %d\n", ret);

	const char image_folder_path[1024] = "/media/sup/8088-3FDF/imgs";

	char** image_list_heap=NULL; 
	int image_num=0;
	ret = util_imlist(&image_list_heap, &image_num, image_folder_path);

	for(int i = 0; i < image_num; i++)
	{
		unsigned char* in_image_heap=NULL;
		int in_image_height = 0, in_image_width = 0, in_image_channel = 0;

		util_imread(&in_image_heap, &in_image_height, & in_image_width, &in_image_channel, image_list_heap[i]);

		unsigned char* warped_image = NULL;
		int warped_image_height=0, warped_image_width=0;
		int landmark[10] = {0,};
		int bbox[4] = {0,};
		float score = 0.0;
		struct IQ iq = {0,};

		printf("image name : %s\n", image_list_heap[i]);

		ret = fcore_detect_add_score(&warped_image, &warped_image_height, &warped_image_width, &score, landmark, bbox, &iq, in_image_heap, in_image_height, in_image_width, 1);

		if(ret == 0)
		{
			printf("Score : %lf\n", score);

			printf("BBOX : %d, %d, %d, %d\n", bbox[0], bbox[1], bbox[2], bbox[3]);

			printf("LANDMARK : %d, %d, %d, %d, %d, %d, %d, %d, %d, %d \n", landmark[0], landmark[1],
				landmark[2], landmark[3], landmark[4], landmark[5], landmark[6], landmark[7], landmark[8], landmark[9]);

			printf("Image Quality ( Luminance Average, Blurness ) : %d, %d\n", iq.y_avg[0], iq.blurness);


			// HPE

			int roi_hpe[4] = {0,};

			roi_hpe[0] = bbox[0] - (bbox[2] / 2);
			roi_hpe[1] = bbox[1] - (bbox[2] / 2);
			roi_hpe[2] = bbox[2] + bbox[2];
			roi_hpe[3] = bbox[3] + bbox[2];

			//init 
			float* out_pose = NULL;// (yaw, pitch ,roll)
			int out_pose_len = 0;

			struct timeval st, nd;
			
			gettimeofday(&st, NULL);

			// Head pose estimation
			ret = fcore_HPE(&out_pose, &out_pose_len, in_image_heap, in_image_height, in_image_width, roi_hpe);

			gettimeofday(&nd, NULL);
			
			long seconds = nd.tv_sec - st.tv_sec;
			long microseconds = nd.tv_usec - st.tv_usec;
			double elapsed_time = seconds * 1000 + microseconds / 1000.0;
			
			printf("time : %.2f ms \n", elapsed_time);

			if (ret == RETURN_OK)
			{
				printf("HPE Result ==> yaw : %lf , pitch : %lf, roll : %lf \n", out_pose[0], out_pose[1], out_pose[2]);
				free(out_pose);
			}
			else
				printf("Head Pose not estimated\n");

			free(warped_image);
		}

		free(in_image_heap);
	}
}

void Test_FAM_ONNX()
{
	int ret = -1;

	print_function_name(__func__);	

	fcore_init();

	fcore_setGpuAvailable(false);

	ret = fcore_init_FD_use_mask(MODEL_PATH);

	printf("ret : %d\n", ret);

	ret = fcore_init_FAM(MODEL_PATH);

	printf("ret : %d\n", ret);

	const char image_folder_path[1024] = "/media/sup/8088-3FDF/imgs";

	char** image_list_heap=NULL; 
	int image_num=0;
	ret = util_imlist(&image_list_heap, &image_num, image_folder_path);

	for(int i = 0; i < image_num; i++)
	{
		unsigned char* in_image_heap=NULL;
		int in_image_height = 0, in_image_width = 0, in_image_channel = 0;

		util_imread(&in_image_heap, &in_image_height, & in_image_width, &in_image_channel, image_list_heap[i]);

		unsigned char* warped_image = NULL;
		int warped_image_height=0, warped_image_width=0;
		int landmark[10] = {0,};
		int bbox[4] = {0,};
		float score = 0.0;
		struct IQ iq = {0,};

		printf("image name : %s\n", image_list_heap[i]);

		ret = fcore_detect_add_score(&warped_image, &warped_image_height, &warped_image_width, &score, landmark, bbox, &iq, in_image_heap, in_image_height, in_image_width, 1);

		if(ret == 0)
		{
			printf("Score : %lf\n", score);

			printf("BBOX : %d, %d, %d, %d\n", bbox[0], bbox[1], bbox[2], bbox[3]);

			printf("LANDMARK : %d, %d, %d, %d, %d, %d, %d, %d, %d, %d \n", landmark[0], landmark[1],
				landmark[2], landmark[3], landmark[4], landmark[5], landmark[6], landmark[7], landmark[8], landmark[9]);

			printf("Image Quality ( Luminance Average, Blurness ) : %d, %d\n", iq.y_avg[0], iq.blurness);


			// Face Attribute
			int age = -1;
			int gender = -1;
			int emotion = -1;
			int glass = -1;
			int race = -1;

			struct timeval st, nd;
			
			gettimeofday(&st, NULL);

			ret = fcore_FAM(&age, &gender, &emotion, &glass, &race, warped_image, warped_image_height, warped_image_width);

			gettimeofday(&nd, NULL);
		
			long seconds = nd.tv_sec - st.tv_sec;
			long microseconds = nd.tv_usec - st.tv_usec;
			double elapsed_time = seconds * 1000 + microseconds / 1000.0;
			
			printf("time : %.2f ms \n", elapsed_time);

			if (ret == RETURN_OK)
			{
				printf("Age : %d \n", age);
				printf("Gender : %d \n", gender);
				printf("Emotion : %d \n", emotion);
				printf("Glass : %d \n", glass);
				printf("Race : %d \n", race);
			}
			else
				printf("Face Attribute not estimated\n");

			free(warped_image);
		}

		free(in_image_heap);
	}
}

void Test_MS_ONNX()
{
	int ret = -1;

	print_function_name(__func__);	

	fcore_init();

	fcore_setGpuAvailable(false);

	ret = fcore_init_FD_use_mask(MODEL_PATH);

	printf("ret : %d\n", ret);

	ret = fcore_init_MS(MODEL_PATH);

	printf("ret : %d\n", ret);

	const char image_folder_path[1024] = "/media/sup/8088-3FDF/imgs";
	const char ms_folder_path[1024] = "/media/sup/8088-3FDF/MS";

	char** image_list_heap=NULL; 
	int image_num=0;
	ret = util_imlist(&image_list_heap, &image_num, image_folder_path);

	for(int i = 0; i < image_num; i++)
	{
		unsigned char* in_image_heap=NULL;
		int in_image_height = 0, in_image_width = 0, in_image_channel = 0;

		util_imread(&in_image_heap, &in_image_height, & in_image_width, &in_image_channel, image_list_heap[i]);

		unsigned char* warped_image = NULL;
		int warped_image_height=0, warped_image_width=0;
		int landmark[10] = {0,};
		int bbox[4] = {0,};
		float score = 0.0;
		struct IQ iq = {0,};

		printf("image name : %s\n", image_list_heap[i]);

		ret = fcore_detect_add_score(&warped_image, &warped_image_height, &warped_image_width, &score, landmark, bbox, &iq, in_image_heap, in_image_height, in_image_width, 1);

		if(ret == 0)
		{
			printf("Score : %lf\n", score);

			printf("BBOX : %d, %d, %d, %d\n", bbox[0], bbox[1], bbox[2], bbox[3]);

			printf("LANDMARK : %d, %d, %d, %d, %d, %d, %d, %d, %d, %d \n", landmark[0], landmark[1],
				landmark[2], landmark[3], landmark[4], landmark[5], landmark[6], landmark[7], landmark[8], landmark[9]);

			printf("Image Quality ( Luminance Average, Blurness ) : %d, %d\n", iq.y_avg[0], iq.blurness);

			// Mask Segmentation
			unsigned char* out_ms_heap = NULL;
			
			struct timeval st, nd;
		
			gettimeofday(&st, NULL);
		
			ret = fcore_MS(&out_ms_heap, warped_image, warped_image_height, warped_image_width);

			gettimeofday(&nd, NULL);

			long seconds = nd.tv_sec - st.tv_sec;	
			long microseconds = nd.tv_usec - st.tv_usec;
			double elapsed_time = seconds * 1000 + microseconds / 1000.0;

	 		printf("time : %.2f ms \n", elapsed_time);

			if (ret == RETURN_OK)
			{
				// save Mask Segmentation Result
				char ms_file_path[1024] = { 0, };
				strcpy(ms_file_path, image_list_heap[i]);
				util_str_replace(ms_file_path, image_folder_path, ms_folder_path);
				util_imwrite(out_ms_heap, 100, 100, ms_file_path);

				free(out_ms_heap);
			}
			else
				printf("Mask Segmentation not estimated\n");

			free(warped_image);
		}

		free(in_image_heap);
	}
}

void Test_FR_ONNX()
{
	int ret = -1;

	print_function_name(__func__);	

	fcore_init();

	fcore_setGpuAvailable(false);

	ret = fcore_init_FR_model_type_1(MODEL_PATH);

	printf("%d\n", ret);

	const char image_folder_path[1024] = "/media/sup/8088-3FDF/FD";
	const char fr_folder_path[1024] = "/media/sup/8088-3FDF/FR";
	
	char** image_list_heap=NULL; 
	int image_num=0;
	ret = util_imlist(&image_list_heap, &image_num, image_folder_path);

	for(int i = 0; i < image_num; i++)
	{
		unsigned char* in_image_heap=NULL;
		int in_image_height = 0, in_image_width = 0, in_image_channel = 0;

		util_imread(&in_image_heap, &in_image_height, & in_image_width, &in_image_channel, image_list_heap[i]);

		unsigned char* feature = NULL;
		int feature_len=0;

		printf("image name : %s\n", image_list_heap[i]);
		
		struct timeval st, nd;
		
		gettimeofday(&st, NULL);

		ret = fcore_extract(&feature, &feature_len, in_image_heap, in_image_height, in_image_width, 1);

		gettimeofday(&nd, NULL);
		
		long seconds = nd.tv_sec - st.tv_sec;
		long microseconds = nd.tv_usec - st.tv_usec;
		double elapsed_time = seconds * 1000 + microseconds / 1000.0;
		
		printf("time : %.2f ms \n", elapsed_time);

		printf("done\n");

		if(ret == RETURN_OK)
		{
			char template_file_path[1024] = { 0, };
			strcpy(template_file_path, image_list_heap[i]);
			util_str_replace(template_file_path, image_folder_path, fr_folder_path);
			util_ext_replace(template_file_path, (char*)"bin");
			FILE* fp = fopen(template_file_path, "wb");
			fwrite(feature, 1, feature_len * sizeof(unsigned char), fp);
			fclose(fp);

			free(feature);
		}

		free(in_image_heap);
	}
}

int main() 
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;
	system("clear");

	// Test_instance();
	// Test_InitSDK();
	// Test_Database();

	// QFE_LoadCamera(sdk_instance);
	// printf("%d\n", QFE_GetNumberOfCamera(sdk_instance));
	// QFE_LoadCamera(sdk_instance);
	// printf("%d\n", QFE_GetNumberOfCamera(sdk_instance));
	// QFE_LoadCamera(sdk_instance);
	// printf("%d\n", QFE_GetNumberOfCamera(sdk_instance));

	// QFE_Image image1;
	// QFE_ReadImageFile(sdk_instance, &image1, IMG_RGB_PATH);

	// QFE_Image image2;
	// QFE_ReadImageFile(sdk_instance, &image2, IMG_RGB_PATH);

	// QFE_FDResult fd_result1;
	// QFE_DetectFaceImage(sdk_instance, &image1, &fd_result1);

	// QFE_FDResult fd_result2;
	// QFE_DetectFaceImage(sdk_instance, &image2, &fd_result2);

	// QFE_VerifyResult verify_result;
	// QFE_VerifyFDResultFDResult(sdk_instance, &fd_result1, &fd_result2, &verify_result);
	// printf("Result: %d\n", verify_result.score);

	// Test_Image();
	// Test_Template();
	// Test_Config();
	// Test_DetectFaceImage();
	// Test_DetectMultiFaceImage();
	// Test_EstimateHeadPoseImage();
	// Test_CheckMaskImage();
	// Test_CheckMaskImage2();
	// Test_EstimateFaceAttributeImage();
	// Test_ExtractTemplateImage();
	// Test_useFDResult();
	// Test_IdentifyImage();
	// Test_IdentifyTemplate();
	// Test_IdentifyFDResult();
	// Test_VerifyImage();
	// Test_Delete();
	// printf("\nTotal] user: %d, templates: %d\n\n", QFE_GetNumberOfAllUsers(sdk_instance), QFE_GetNumberOfAllTemplates(sdk_instance));
	// Test_EnrollImage();
	// printf("\nTotal] user: %d, templates: %d\n\n", QFE_GetNumberOfAllUsers(sdk_instance), QFE_GetNumberOfAllTemplates(sdk_instance));
	// Test_DeleteAll();	
	// printf("\nTotal] user: %d, templates: %d\n\n", QFE_GetNumberOfAllUsers(sdk_instance), QFE_GetNumberOfAllTemplates(sdk_instance));
	
	// Test_Image_Base64();
	// Test_Template_Base64();

	// Test_HPE_Multi();
	// Test_MS_Multi();
	// Test_FE_Multi();

	//Test_FD_ONNX();
	Test_FR_ONNX();
	//Test_HPE_ONNX();
	//Test_MS_ONNX();
	//Test_FAM_ONNX();

	//sleep(100); // sleep 100 seconds

	// QFE_ReleaseInstance(sdk_instance);	

	return 0;
}
