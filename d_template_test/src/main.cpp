#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <sys/stat.h>
#include <vector>
#include <dirent.h>
#include <sys/stat.h>
#include <errno.h>

#include "QFE_API.hpp"

#define MODEL_PATH "model"

QFE_SDK *sdk_instance = nullptr;

char *fr_model_path = NULL;
char *input_directory_path = NULL;
char *output_directory_path = NULL;

int check_directory_exists(const char *path) 
{
    struct stat st;
    if (stat(path, &st) == 0 && S_ISDIR(st.st_mode)) 
    {
        return 1;
    }
    return 0;
}

void create_directory(const char *path) 
{
    if (mkdir(path, 0755) == -1) 
    {
        if (errno == EEXIST) 
        {
            printf("Output directory already exists: %s\n", path);
        } 
        else 
        {
            perror("Error creating output directory");
            exit(-1);
        }
    } 
    else 
    {
        printf("Output directory created: %s\n", path);
    }
}

void validate_directories(const char *input_dir, const char *output_dir) 
{
    if (!check_directory_exists(input_dir)) 
    {
        fprintf(stderr, "Error: Input directory does not exist: %s\n", input_dir);
        exit(-1);
    }

    if (!check_directory_exists(output_dir)) 
    {
        printf("Output directory does not exist. Creating: %s\n", output_dir);
        create_directory(output_dir);
    }
}

std::vector<std::string> getFileNamesInDirectory(const std::string& directoryPath) {
    std::vector<std::string> fileNames;
    DIR* dir = opendir(directoryPath.c_str());

    if (dir == nullptr) {
        std::cerr << "Error: Could not open directory " << directoryPath << std::endl;
        return fileNames;
    }

    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        std::string fileName = entry->d_name;

        if (fileName == "." || fileName == "..") {
            continue;
        }

        std::string fullPath = directoryPath + "/" + fileName;

        struct stat fileInfo;
        if (stat(fullPath.c_str(), &fileInfo) == 0 && S_ISREG(fileInfo.st_mode)) {
            fileNames.push_back(fullPath);
        }
    }

    closedir(dir);
    return fileNames;
}

bool copyFile(const std::string& src, const std::string& dest) {
    std::ifstream srcFile(src, std::ios::binary);
    std::ofstream destFile(dest, std::ios::binary);
    if (!srcFile || !destFile) {
        std::cerr << "Error: Could not open file for copying: " << src << " to " << dest << std::endl;
        return false;
    }
    destFile << srcFile.rdbuf();
    return true;
}

std::string getFileExtension(const std::string& fileName) {
    size_t dotPos = fileName.find_last_of('.');
    if (dotPos == std::string::npos) return "";
    return fileName.substr(dotPos + 1);
}

char* createDatFileName(const char* jpgFileName) {
    std::string fileName(jpgFileName);

    size_t dotPos = fileName.rfind(".jpg");
    if (dotPos == std::string::npos) {
        std::cerr << "Error: Not a .jpg file" << std::endl;
        return nullptr;
    }
    fileName.replace(dotPos, 4, ".dat");

    size_t imagePos = fileName.find("image");
    if (imagePos != std::string::npos) {
        fileName.replace(imagePos, 5, "template");
    }

    fileName = fileName.substr(fileName.find_last_of('/') + 1);

    char* datFileName = new char[fileName.length() + 1];
    std::strcpy(datFileName, fileName.c_str());

    return datFileName;
}

std::string getFileNameOnly(const std::string& filePath) {
    size_t lastSlashPos = filePath.find_last_of("/\\");
    if (lastSlashPos == std::string::npos) {
        return filePath;
    }
    return filePath.substr(lastSlashPos + 1);
}

void print_usage(const char *program_name) 
{
    printf("Usage: %s -m [model_path] -i [input_directory_path] -o [output_directory_path]\n", program_name);
    printf("Options:\n");
    printf("\t-m, --model=<model_path>\t\tSpecify the model file path.\n");
    printf("\t-i, --input=<input_directory_path>\tSpecify the input directory path.\n");
    printf("\t-o, --output=<output_directory_path>\tSpecify the output directory path.\n");
    printf("\n");
    printf("\t-h, --help\t\t\t\tShow this help message.\n");
}

void parse_input(int argc, char *argv[]) 
{   
    if (argc < 7) 
    {
        print_usage(argv[0]);
        exit(-1);
    }

    for (int i = 1; i < argc; i++) 
    {
        if (strcmp(argv[i], "--help") == 0 || strcmp(argv[i], "-h") == 0) 
        {
            print_usage(argv[0]);
            exit(0);
        } 
        else if ((strcmp(argv[i], "-m") == 0 || strncmp(argv[i], "--model=", 8) == 0) && i + 1 < argc) 
        {
            fr_model_path = (argv[i][1] == '-') ? argv[i] + 8 : argv[++i];
        } 
        else if ((strcmp(argv[i], "-i") == 0 || strncmp(argv[i], "--input=", 8) == 0) && i + 1 < argc) 
        {
            input_directory_path = (argv[i][1] == '-') ? argv[i] + 8 : argv[++i];
        } 
        else if ((strcmp(argv[i], "-o") == 0 || strncmp(argv[i], "--output=", 9) == 0) && i + 1 < argc) 
        {
            output_directory_path = (argv[i][1] == '-') ? argv[i] + 9 : argv[++i];
        }
    }

    if (fr_model_path == NULL || input_directory_path == NULL || output_directory_path == NULL) 
    {
        printf("Error: Missing required arguments.\n");
        print_usage(argv[0]);
        exit(-1);
    }

	validate_directories(input_directory_path, output_directory_path);
}

int main(int argc, char *argv[]) 
{
	QFE_RET_CODE ret = QFE_ERR_UNKNOWN;

	parse_input(argc, argv);	

	sdk_instance = new QFE_SDK();

	ret = sdk_instance->LoadModel_Template_Test(MODEL_PATH, fr_model_path);
	if (ret == QFE_RET_SUCCESS)
	{
		const char* sdk_version = sdk_instance->GetSDKVersionString();
	}
	else
	{
		printf("\tModel Load Failed: %d\n", ret);
		exit(-1);
	}

    std::vector<std::string> files = getFileNamesInDirectory(input_directory_path);

    for (const auto& filePath : files) {
        std::string fileName = filePath.substr(filePath.find_last_of('/') + 1);
        std::string extension = getFileExtension(fileName);

        if (extension == "jpg") {
            QFE::Image test_image;

            sdk_instance->ReadImageFile(test_image, filePath.c_str());

            QFE::Template test_template;

            sdk_instance->ExtractTemplateImage(test_image, test_template);

            char* datFileName = createDatFileName(filePath.c_str());
            std::string outputPath = std::string(output_directory_path) + "/" + datFileName;

            sdk_instance->WriteTemplateFile(test_template, outputPath.c_str());     

            delete[] datFileName;
        } 
        else if (extension == "dat") {
            std::string destPath = std::string(output_directory_path) + "/" + fileName;

            copyFile(filePath, destPath);
        }
    }

    QFE::Template template1;
    QFE::Template template2;

    int maxScore = 0;
    int minScore = 100000;
    std::string maxFilePair1, maxFilePair2;
    std::string minFilePair1, minFilePair2;

    std::vector<std::string> out_files = getFileNamesInDirectory(output_directory_path);

    for (const auto& file1 : out_files) {
        std::cout << "---------------------------------------------------------------" << std::endl;
        for (const auto& file2 : out_files) {
            if (file1 == file2) { 
                continue;
            }

            sdk_instance->ReadTemplateFile(template1, file1.c_str());
            sdk_instance->ReadTemplateFile(template2, file2.c_str());

            QFE_VerifyResult verify_Result;
            sdk_instance->VerifyTemplateTemplate(template1, template2, verify_Result);

            std::string fileName1 = getFileNameOnly(file1);
            std::string fileName2 = getFileNameOnly(file2);

            std::cout << std::left << std::setw(25) << fileName1 
                      << " VS\t" 
                      << std::setw(25) << fileName2 
                      << ": " << verify_Result.score << std::endl;

            if (verify_Result.score > maxScore) {
                maxScore = verify_Result.score;
                maxFilePair1 = fileName1;
                maxFilePair2 = fileName2;
            }
            if (verify_Result.score < minScore) {
                minScore = verify_Result.score;
                minFilePair1 = fileName1;
                minFilePair2 = fileName2;
            }
        }
    }

    std::cout << std::endl;
    std::cout << "\nMaximum Score: " << maxScore 
              << " ( " << maxFilePair1 << " / " << maxFilePair2 << " ) " << std::endl;
    std::cout << "Minimum Score: " << minScore
              << " ( " << minFilePair1 << " / " << minFilePair2 << " ) " << std::endl;

	return 0;
}