# libsodium.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.7
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libsodium.so.23'

# Names of this library.
library_names='libsodium.so.23.3.0 libsodium.so.23 libsodium.so'

# The name of the static archive.
old_library='libsodium.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=' -pthread'

# Libraries that this one depends upon.
dependency_libs=' -lpthread'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libsodium.
current=26
age=3
revision=0

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/sodium_arm64/install_arm64/lib'
