#ifndef __CIPHER_H__
#define __CIPHER_H__

// error code
#define CIPHER_ERR_SUCCESS 0
#define CIPHER_ERR_FAILURE -1
#define CIPHER_ERR_INVLID_KEY_LENGTH -2

// maximum key length
#define CIPHER_MAXIMUM_PRIVATE_KEY_LENGTH 32

// maximum nonce length
#define CIPHER_MAXIMUM_NONCE_LENGTH 24

#ifdef __cplusplus
extern "C" {
#endif

// xor operation
int cipher(const unsigned char *input, unsigned char *output, int len);

#ifdef __cplusplus
}
#endif
#endif // __CIPHER_H__