#ifndef LICENSE_MANAGER_H
#define LICENSE_MANAGER_H

#include <string>
#include <stdexcept>

class LicenseException : public std::runtime_error
{
public:
    explicit LicenseException(const std::string &message);
};

class LicenseManager
{
private:
    const void *toPlatformString(const std::string &str);
    void checkError(int status, const std::string &errorMsg);

public:
    LicenseManager();
    ~LicenseManager();

    // Cryptlex version
    int get_library_version(std::string &version);
    int set_cache_mode(int enable);
    // Essential
    int set_product_data(const std::string &product_data);
    int set_product_file(const std::string &product_data_path);
    int set_product_id(const std::string &product_id);
    int set_product_id_flag(const std::string &product_id, uint32_t &flag);
    int set_activation_key(const std::string &activation_key);

    // Validity
    int is_license_genuine();

    // Metadata (Max instance)
    int set_activation_metadata(const std::string &key, std::string &value); // for developer
    int get_activation_metadata(std::string &key, std::string &value);
    int get_license_metadata(std::string &key, std::string &value);

    // Activation / Deactivation
    int activate_license();
    int deactivate_license();

    int reset_license(); // for developer

    // Online Trial License Methods
    int activate_trial_license();
    int is_trial_license_genuine();

    // Offline License Methods
    int generate_offline_activation_request(const std::string &file_path);
    int activate_license_offline(const std::string &file_path);
    int deactivate_license_offline(const std::string &file_path);

    int generate_offline_trial_activation_request(const std::string &file_path);
    int activate_trial_license_offline(const std::string &file_path);
    int deactivate_trial_license_offline(const std::string &file_path);
};

#endif // LICENSE_MANAGER_H
