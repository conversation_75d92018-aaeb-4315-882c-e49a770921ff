#include "algorithm.h"
#include <mutex>

Algo::Algo() {}

Algo::Algo(bool do_init) {
	// Make sure we can initialize,
	// without default network model
	// location
	if (do_init) {
		for (int i = 0; i < infernet.size(); i++) {
			infernet[i]->init_FD();
			infernet[i]->init_FR();
			infernet[i]->init_POSE();
			infernet[i]->init_FAS();
			infernet[i]->init_MS();
		}
	}
	// Else, do nothing
	// init manually
}

void Algo::release() {
	for (auto& net : infernet) {
		delete net;
	}
	infernet.clear();
}

Algo::~Algo() {
	release();
}

int Algo::initNet(int thread_num)
{
	// create inferenceNet as much as thread_num using std::vector
	for (int i = 0; i < thread_num; i++) {

		InferenceNet* infer = new InferenceNet;

		// change emplace_back from push_back
		// "unique_ptr" ocuur error at "push_back"
		infernet.emplace_back(infer);
	}

	return (thread_num == infernet.size() ? 1 : -1);
}

void Algo::setConfigBackend(const int backend)
{
	for (int i = 0; i < infernet.size(); i++) {
		infernet[i]->setConfigBackend(backend);
	}
}

void Algo::setConfigSerializationDir(const std::string serialization_dir)
{
	for (int i = 0; i < infernet.size(); i++) {
		infernet[i]->setConfigSerializationDir(serialization_dir);
	}
}

int Algo::CheckGpuAvailable()
{
	for (int i = 0; i < infernet.size(); i++) {
		if (infernet[i]->check_gpu_avaliable() == -1) {
			return -1;
		}
	}

	return 0;
}

int Algo::SetGpuAvailable(bool gpu)
{
	for (int i = 0; i < infernet.size(); i++) {
		if (infernet[i]->set_gpu(gpu) == -1) {
			return -1;
		}
	}

	return 0;
}

int Algo::initFDNet(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FD();
		else
			status = infernet[i]->init_FD(model);
	}
	return status;
}

int Algo::initFDNet_landscape(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FD_landscape();
		else
			status = infernet[i]->init_FD_landscape(model);
	}
	return status;
}

int Algo::initFDNet2(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FD2();
		else
			status = infernet[i]->init_FD2(model);
	}
	return status;
}

int Algo::initFDNet_mask(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FD_mask();
		else
			status = infernet[i]->init_FD_mask(model);
	}
	return status;
}

int Algo::initFDNet2_mask(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FD2_mask();
		else
			status = infernet[i]->init_FD2_mask(model);
	}
	return status;
}

int Algo::initFDNet_yolo_landscape(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FD_yolo_landscape();
		else
			status = infernet[i]->init_FD_yolo_landscape(model);
	}
	return status;
}

int Algo::initFRNet(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FR();
		else
			status = infernet[i]->init_FR(model);
	}
	return status;
}

int Algo::initFRNet_mask(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FR_mask();
		else
			status = infernet[i]->init_FR_mask(model);
	}
	return status;
}

int Algo::initFRNet_no_mask(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FR_no_mask();
		else
			status = infernet[i]->init_FR_no_mask(model);
	}
	return status;
}



int Algo::initPoseNet(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_POSE();
		else
			status = infernet[i]->init_POSE(model);
	}
	return status;
}

int Algo::initFASNet(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FAS();
		else
			status = infernet[i]->init_FAS(model);
	}
	return status;
}

int Algo::initFASNet_NIR(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FAS_NIR();
		else
			status = infernet[i]->init_FAS_NIR(model);
	}
	return status;
}

int Algo::initMSNet(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_MS();
		else
			status = infernet[i]->init_MS(model);
	}
	return status;
}

int Algo::initFAMNet(std::string& model) {
	int status = 1;

	for (int i = 0; i < infernet.size(); i++) {
		if (model == "\0")
			status = infernet[i]->init_FAM();
		else
			status = infernet[i]->init_FAM(model);
	}
	return status;
}

float clip(float n, float lower, float upper)
{
	return std::max(lower, std::min(n, upper));
}

//if input image is too big, then resize.
int Algo::adjust_high_resolution_image(cv::Mat& out_image, float* down_sample_ratio, const cv::Mat in_image)
{
	// if (in_image.empty())
	// 	return -1;

	// // assume maximum resolution is 2420 x 1815;
	// int max_resolution = 2420;
	// int min_resolution = 1280;

	// // calculate down sample ratio.
	// float ratio = std::max(in_image.rows, in_image.cols) / (float)min_resolution;
	// //float ratio = std::max(in_image.rows, in_image.cols) / (float)max_resolution;

	// // if input is not big enough, return input. 
	// if (ratio <= 1.5)
	// {
	// 	out_image = in_image;
	// 	*down_sample_ratio = 1.0;
	// 	return 1;
	// }

	// *down_sample_ratio = ratio;

	// // calculate down sample image w, h.
	// int downsample_h = in_image.rows / *down_sample_ratio;
	// int downsample_w = in_image.cols / *down_sample_ratio;

	// cv::Mat blur_img;

	// // resize & blur.
	// cv::GaussianBlur(in_image, blur_img, cv::Size(7, 7), 20.0);

	// // calculate gamma & clip.
	// float gamma = (std::max(in_image.rows, in_image.cols) - min_resolution) / (float)(max_resolution - min_resolution);
	// float gamma_clip = clip(gamma, 0.0, 1.0);

	// // calculate out image.
	// out_image = (1 - gamma_clip) * in_image + gamma_clip * blur_img;

	// cv::resize(out_image, out_image, cv::Size(downsample_w, downsample_h));

	// return 1;
	out_image = in_image;
	*down_sample_ratio = 1.0;
	return 1;
}

int Algo::detectFace2s_new(float* score_out, float* score1, std::vector<float>& ldm, std::vector<float>& ldm1, std::vector<float>& bbox, std::vector<float>& bbox1, cv::Mat& out_image,
	const cv::Mat& in_image, int use_mask, int tid, std::vector<std::string> vname)
{
	cv::Mat in_image_rsz, img250, img112;

	tic();
	cv::cvtColor(in_image, in_image_rsz, cv::COLOR_RGB2BGR);
	//printf("tid : %d,", tid);
	toc("FaceCore", "Face Detection", "cvtcolor");

	// First stage
	tic();
	setConfigDetection(0.1f, 0.1f, 0.2f);
	int stat1 = detectFace(score1, ldm1, bbox1, in_image_rsz, 0, tid, 1); // change from use_mask to 0
	toc("FaceCore", "Face Detection", "1st");
	if (stat1 < 0) { return -1; }

	// Second stage
	setConfigDetection(0.8f, 0.8f, 0.2f);

	int roi[4] = { 0, };

	// case 2 : Expanding up, down, left, and right by half the width of the bounce box.
	roi[0] = bbox1[0] - (bbox1[2] / 2);
	roi[1] = bbox1[1] - (bbox1[2] / 2);
	roi[2] = bbox1[2] + (bbox1[2]);
	roi[3] = bbox1[3] + (bbox1[2]);

	std::vector<int> bbox_in(roi, roi + 4);

	tic();
	int stat2 = detectFace_crop(score_out, ldm, bbox, in_image_rsz, bbox_in, use_mask, tid);
	toc("FaceCore", "Face Detection", "2nd done");
	if (stat2 < 0) { return -1; }

	// if input is not big enough, return input.  
	if (std::max(bbox[2], bbox[3]) > 400) // bbox width or bbox height > 500
	{
		std::vector<float> tmp_ldm(ldm);
		for (int i = 0; i < 10; i++)
		{
			tmp_ldm[i] *= downsample_ratio;
		}
		warpImage(img112, input_img_rsz, tmp_ldm, 2);
	}
	else
	{
		tic();
		warpImage(img112, in_image_rsz, ldm, 2);
		//printf("tid : %d,", tid);
		toc("FaceCore", "Face Detection", "warp origin to 112");
	}

	cv::cvtColor(img112, out_image, cv::COLOR_BGR2RGB);

	return 1;
}

int Algo::detectFace2s(float* score_out, float* score1, std::vector<float>& ldm, std::vector<float>& ldm1, std::vector<float>& bbox, std::vector<float>& bbox1, cv::Mat& out_image,
	const cv::Mat& in_image, int use_mask, int tid, std::vector<std::string> vname) {
	tic();

	// Variable init
	//std::vector<float> ldm1;
	cv::Mat in_image_rsz, img250, img112;

	// Resize image
	//cv::resize(in_image, in_image_rsz, cv::Size(320, 640));
	//in_image_rsz = in_image.clone();
	//toc("FaceCore", "Face Detection", "Clone input image");

	tic();
	cv::cvtColor(in_image, in_image_rsz, cv::COLOR_RGB2BGR);
	toc("FaceCore", "Face Detection", "Convert input color");

	// detectFace2s -- use_mask 1
	//	1st stage->use_mask 0
	//	2nd stage->use_mask 1
	// detectFace2s -- use_mask 0
	//	1st stagee-- > use_mask 0
	//	2nd stage-- > use_mask 0

	// First stage
	tic();
	setConfigDetection(0.1f, 0.1f, 0.2f);
	int stat1 = detectFace(score1, ldm1, bbox1, in_image_rsz, 0, tid, 1); // change from use_mask to 0
	if (stat1 < 0) { return -1; }
	warpImage(img250, in_image_rsz, ldm1, 1);
	toc("FaceCore", "Face Detection", "Convert output color");
	//cv::cvtColor(img250, out_image1, cv::COLOR_BGR2RGB);	
	//out_image1 = img250.clone();

	// Second stage
	tic();
	setConfigDetection(0.7f, 0.7f, 0.2f);
	//setConfigWarping(false, 1, 1);
	int stat2 = detectFace(score_out, ldm, bbox, img250, use_mask, tid, 2);
	if (stat2 < 0) { return -1; }
	warpImage(img112, img250, ldm, 2);
	toc("FaceCore", "Face Detection", "2nd");

	cv::cvtColor(img112, out_image, cv::COLOR_BGR2RGB);

	return 1;
}

int Algo::detectFaceIRFast_v2(float* score, std::vector<float>& ldm_out, std::vector<float>& bbox_out, cv::Mat& out_image,
	const cv::Mat& in_image_ir, std::vector<int>& bbox1_in, int use_mask,
	int tid, std::vector<std::string>vname) {

	cv::Mat img112, in_image_rsz;

	// Resize to 250x250 for detection
	cv::cvtColor(in_image_ir, in_image_rsz, cv::COLOR_RGB2BGR);

	// Detection usings stage 2
	int stat = detectFace_FromRoi(score, ldm_out, bbox_out, in_image_rsz, bbox1_in, 1, tid);
	if (stat < 0) {
		return -1;
	}
	warpImage(img112, in_image_rsz, ldm_out, 2);

	cv::cvtColor(img112, out_image, cv::COLOR_BGR2RGB);

	return 1;
}

int Algo::detectFace_crop(float* score, std::vector<float>& ldm_out, std::vector<float>& bbox_out,
	const cv::Mat& in_image_ir, std::vector<int>& bbox1_in, int use_mask,
	int tid, std::vector<std::string>vname) {

	float max_w = in_image_ir.size().width;
	float max_h = in_image_ir.size().height;

	float x = bbox1_in[0];
	float y = bbox1_in[1];
	float w = bbox1_in[2];
	float h = bbox1_in[3];

	// Make bbox into square
	float offset_x = 0.f, offset_y = 0.f;
	if (w > h) {
		offset_y = (w - h) / 2;
	}
	else {
		offset_x = (h - w) / 2;
	}

	int expand_letf = 0;
	int expand_right = 0;
	int expand_top = 0;
	int expand_bottom = 0;

	// Prevent out of image
	if (x < 0)
	{
		expand_letf = abs(x);
		x = 0;
	}
	if (y < 0)
	{
		expand_top = abs(y);
		y = 0;
	}
	if ((x + w) > in_image_ir.cols)
	{
		expand_right = abs((x + w) - in_image_ir.cols);
	}
	if ((y + h) > in_image_ir.rows)
	{
		expand_bottom = abs((y + h) - in_image_ir.rows);
	}

	cv::Mat img_border;
	cv::copyMakeBorder(in_image_ir, img_border, expand_top, expand_bottom, expand_letf, expand_right, cv::BORDER_CONSTANT);

	// Crop Image to ROI
	cv::Rect roi(x, y, w, h);
	cv::Mat img_roi = img_border(roi).clone();

	float downsample_ratio = std::max(img_roi.rows / (float)250, img_roi.cols / (float)250);
	int downsample_h = img_roi.rows / downsample_ratio;
	int downsample_w = img_roi.cols / downsample_ratio;

	// Resize to 250x250 for detection
	cv::resize(img_roi, img_roi, cv::Size(downsample_w, downsample_h), cv::INTER_CUBIC);

	// Detection usings stage 2
	int stat = detectFace(score, ldm_out, bbox_out, img_roi, use_mask, tid, 2);
	if (stat < 0) {
		return -1;
	}

	bbox_out[0] = ((bbox_out.at(0) / downsample_w * w) + x - expand_letf);
	bbox_out[1] = ((bbox_out.at(1) / downsample_h * h) + y - expand_top);
	bbox_out[2] = (bbox_out.at(2) / downsample_w * w);
	bbox_out[3] = (bbox_out.at(3) / downsample_h * h);

	for (int i = 0; i < 10; i += 2) {
		ldm_out[i] = (ldm_out[i] / downsample_w * w) + x - expand_letf;
		ldm_out[i + 1] = (ldm_out[i + 1] / downsample_h * h) + y - expand_top;
	}

	return 1;
}

int Algo::detectFace_FromRoi(float* score, std::vector<float>& ldm_out, std::vector<float>& bbox_out,
	const cv::Mat& in_image_ir, std::vector<int>& bbox1_in, int use_mask,
	int tid, std::vector<std::string>vname) {

	float max_w = in_image_ir.size().width;
	float max_h = in_image_ir.size().height;

	float x = bbox1_in[0];
	float y = bbox1_in[1];
	float w = bbox1_in[2];
	float h = bbox1_in[3];

	// Make bbox into square
	float offset_x = 0.f, offset_y = 0.f;
	if (w > h) {
		offset_y = (w - h) / 2;
	}
	else {
		offset_x = (h - w) / 2;
	}

	// Prevent out of boundaries
	x = std::max(x - offset_x, 0.f);
	y = std::max(y - offset_y, 0.f);
	w = std::min(w + offset_x * 2, max_w);
	h = std::min(h + offset_y * 2, max_h);

	// Prevent out of image
	if (x < 0)
	{
		int tmp_x = 0;
		x = 0;
		w = w + abs(tmp_x);
	}
	if (y < 0)
	{
		int tmp_y = 0;
		y = 0;
		h = h + abs(tmp_y);
	}
	if ((x + w) > in_image_ir.cols)
	{
		int tmp_w = w;
		w = w + (in_image_ir.cols - (x + w));
		x = x + (in_image_ir.cols - (x + tmp_w));
	}
	if ((y + h) > in_image_ir.rows)
	{
		int tmp_h = h;
		h = h + (in_image_ir.rows - (y + h));
		y = y + (in_image_ir.rows - (y + tmp_h));
	}

	//// Expand bounding box
	//float expand_rate = 0.2f; // hyper parameters (0.1, 0.15, 0.2)
	//float expansion = w * expand_rate;

	//// Prevent out of boundaries
	//x = std::max(x - expansion, 0.f);
	//y = std::max(y - expansion, 0.f);
	//w = std::min(w + expansion * 2, max_w - x);
	//h = std::min(h + expansion * 2, max_h - y);

	// Crop Image to ROI
	cv::Rect roi(x, y, w, h);
	cv::Mat img_roi = in_image_ir(roi).clone();

	float downsample_ratio = std::max(img_roi.rows / (float)250, img_roi.cols / (float)250);
	int downsample_h = img_roi.rows / downsample_ratio;
	int downsample_w = img_roi.cols / downsample_ratio;

	// Resize to 250x250 for detection
	cv::resize(img_roi, img_roi, cv::Size(downsample_w, downsample_h));

	// Detection usings stage 2
	int stat = detectFace(score, ldm_out, bbox_out, img_roi, use_mask, tid, 2);
	if (stat < 0) {
		return -1;
	}

	// check whether detected face is fliped (horizontal or vertical).
	// if ((ldm_out[1] > ldm_out[7]) && (ldm_out[3] > ldm_out[9]))
	// 	return -1;

	bbox_out[0] = ((bbox_out.at(0) / downsample_w * w) + x);
	bbox_out[1] = ((bbox_out.at(1) / downsample_h * h) + y);
	bbox_out[2] = (bbox_out.at(2) / downsample_w * w);
	bbox_out[3] = (bbox_out.at(3) / downsample_h * h);

	for (int i = 0; i < 10; i += 2) {
		ldm_out[i] = (ldm_out[i] / downsample_w * w) + x;
		ldm_out[i + 1] = (ldm_out[i + 1] / downsample_h * h) + y;
	}

	return 1;
}


int Algo::detectFace(float* score, std::vector<float>& ldm, std::vector<float>& bbox,
	const cv::Mat& in_image, int use_mask, int tid, int stage,
	std::vector<std::string> vname) {

	int status;
	cv::Mat in_image_rsz;

	if (stage == 1)
		_verbose = false;

	tic();

	// If stage 1, resize half
	if (stage == 1) {
		// if (in_image.rows >= in_image.cols)
		// {
		// 	float downsample_ratio = std::max(in_image.rows / (float)640, in_image.cols / (float)384);
		// 	int downsample_h = in_image.rows / downsample_ratio;
		// 	int downsample_w = in_image.cols / downsample_ratio;
		// 	cv::resize(in_image, in_image_rsz, cv::Size(downsample_w, downsample_h));
		// }
		// else
		// {
		// 	float downsample_ratio = std::max(in_image.rows / (float)384, in_image.cols / (float)640);
		// 	int downsample_h = in_image.rows / downsample_ratio;
		// 	int downsample_w = in_image.cols / downsample_ratio;
		// 	cv::resize(in_image, in_image_rsz, cv::Size(downsample_w, downsample_h));
		// }

		// upgrade
		if (in_image.rows >= in_image.cols)
		{
			downsample_ratio = std::min(640 / (float)in_image.rows, 384 / (float)in_image.cols);
			cv::resize(in_image, in_image_rsz, cv::Size(0, 0), downsample_ratio, downsample_ratio, cv::INTER_AREA);
			input_img_rsz = in_image_rsz.clone();
		}
		else
		{
			downsample_ratio = std::min(384 / (float)in_image.rows, 640 / (float)in_image.cols);
			cv::resize(in_image, in_image_rsz, cv::Size(0, 0), downsample_ratio, downsample_ratio, cv::INTER_AREA);
			input_img_rsz = in_image_rsz.clone();
		}

	}
	else {
		in_image_rsz = in_image;
	}
	//printf("tid : %d,", tid);
	toc("FaceCore", "Face Detection", "resize input");

	tic();
	// Make FD network handle dynamic input
	// All input size is possible (new)
	//std::vector<int> sz_and_off = getDynamicInputSize(in_image_rsz.size());
	//cv::Size dyn_fd_sz = { sz_and_off[1], sz_and_off[0] };
	cv::Size dyn_fd_sz;
	if (stage == 1)
		if (in_image.rows >= in_image.cols)
			dyn_fd_sz = { 384 , 640 };
		else
			dyn_fd_sz = { 640, 384 };
	else
		dyn_fd_sz = { 256, 256 };

	setConfigFDSize(dyn_fd_sz); // set new size for PriorBox
	//printf("tid : %d,", tid);
	toc("FaceCore", "Face Detection", "set dynamic size");

	// [Optimization]
	// If pbox has been init before && current-prev image size are same
	// skip making prior box ----> save time
	bool skip_prior_box = false;
	if (stage == 1) {
		if (!_prior_box.empty() && (dyn_fd_sz == _fd_size_prev)) {
			skip_prior_box = true;
		}
		_fd_size_prev = dyn_fd_sz;
	}
	// if stage 2, init one time only
	if (stage == 2) {
		skip_prior_box = !_prior_box2.empty();
	}

	tic();
	// Add padding to input image
	//int bot = sz_and_off[2];
	//int right = sz_and_off[3];
	int bot = dyn_fd_sz.height - in_image_rsz.rows;
	int right = dyn_fd_sz.width - in_image_rsz.cols;
	cv::Mat in_image_pad;
	cv::copyMakeBorder(in_image_rsz, in_image_pad, 0, bot, 0, right, cv::BORDER_CONSTANT);
	//printf("tid : %d,", tid);
	toc("FaceCore", "Face Detection", "copy image border");

	// Inference Network
	std::vector<cv::Mat> out_vtensor = std::vector<cv::Mat>(3);
	infernet[tid]->setConfigFDSize(dyn_fd_sz); // set new size for FD-Net

	std::vector<std::string> node_name = { "box", "cls", "ldm" };

	if (stage == 2) {
		status = infernet[tid]->infer_FD2(out_vtensor, in_image_pad, use_mask);
	}
	else {
		if (in_image.rows >= in_image.cols)
			//status = infernet[tid]->infer_FD_no_mask(out_vtensor, in_image_pad, node_name);
			status = infernet[tid]->infer_FD(out_vtensor, in_image_pad, use_mask, node_name);
		else
			status = infernet[tid]->infer_FD_landscape(out_vtensor, in_image_pad, use_mask, node_name);
	}

	if (status < 0)
	{
		return -1;
	}

	// Post processing
	tic();
	if (!skip_prior_box) {
		if (stage == 2) { makePriorBox(_prior_box2); }
		else { makePriorBox(_prior_box); }
	}
	//printf("tid : %d,", tid);
	toc("FaceCore", "Face Detection", "make prior box");

	tic();
	status = postProcOutput(score, ldm, bbox, out_vtensor, in_image_rsz, stage);
	//#ifdef _DEBUG
	//	logging.debug("Post processing");
	//#endif
	//printf("tid : %d,", tid);
	toc("FaceCore", "Face Detection", "Post Processing");

	if (status < 0)
	{
		return -1;
	}

	tic();
	// Fix scaling	
	float sx = (float)in_image.cols / in_image_rsz.cols;
	float sy = (float)in_image.rows / in_image_rsz.rows;
	for (int i = 0; i < 10; i += 2) {
		ldm[i] *= sx;
		ldm[i + 1] *= sy;
	}
	for (int i = 0; i < 4; i += 2)
	{
		bbox[i] *= sx;
		bbox[i + 1] *= sy;
	}
	//printf("tid : %d,", tid);
	toc("FaceCore", "Face Detection", "rescale landmark");

	// Warping
	// after 0.12.1, implement warp in "detectfaces"
	/*tic();
	warpImage(out_image, in_image, ldm, stage);
	toc("FaceCore", "Face Detection", "warp");*/

	if (stage == 1)
		_verbose = false;

	return 1;
}

int Algo::detect_yolo_landscape(std::vector<cv::Mat>& output_tensors, float* downsample_ratio,
	const cv::Mat& in_image, const cv::Size& model_input_size, int tid) {

	std::cout << "[DEBUG] detect_yolo_landscape called" << std::endl;
	std::cout << "[DEBUG] in_image size: " << in_image.cols << "x" << in_image.rows << std::endl;
	std::cout << "[DEBUG] landscape model_input_size: " << model_input_size.width << "x" << model_input_size.height << std::endl;

	// 1. Image preprocessing
	cv::Mat in_image_rsz;
	*downsample_ratio = std::min((float)model_input_size.width / in_image.cols, (float)model_input_size.height / in_image.rows);
	std::cout << "[DEBUG] downsample_ratio: " << *downsample_ratio << std::endl;
	
	cv::resize(in_image, in_image_rsz, cv::Size(), *downsample_ratio, *downsample_ratio, cv::INTER_LINEAR);
	std::cout << "[DEBUG] in_image_rsz size: " << in_image_rsz.cols << "x" << in_image_rsz.rows << std::endl;

	// 2. Add padding
	cv::Mat in_image_pad;
	int pad_bottom = model_input_size.height - in_image_rsz.rows;
	int pad_right = model_input_size.width - in_image_rsz.cols;
	cv::copyMakeBorder(in_image_rsz, in_image_pad, 0, pad_bottom, 0, pad_right,
		cv::BORDER_CONSTANT, cv::Scalar(114, 114, 114));

	// 3. Run inferences	
	//std::vector<cv::Mat> output_tensors = std::vector<cv::Mat>(4);		// Defined externally.

	int status = infernet[tid]->infer_FD_yolo_landscape(output_tensors, in_image_pad);

	if (status < 0)
		return -1;

	return 1;
}


int Algo::detect_yolo_square(std::vector<cv::Mat>& output_tensors, float* downsample_ratio,
	const cv::Mat& in_image, const cv::Size& model_input_size, int tid) {

	std::cout << "[DEBUG] detect_yolo_square called" << std::endl;
	std::cout << "[DEBUG] in_image size: " << in_image.cols << "x" << in_image.rows << std::endl;
	std::cout << "[DEBUG] square model_input_size: " << model_input_size.width << "x" << model_input_size.height << std::endl;

	// 1. Image preprocessing for portrait
	cv::Mat in_image_rsz;
	*downsample_ratio = std::min((float)model_input_size.width / in_image.cols, (float)model_input_size.height / in_image.rows);
	std::cout << "[DEBUG] downsample_ratio: " << *downsample_ratio << std::endl;
	
	cv::resize(in_image, in_image_rsz, cv::Size(), *downsample_ratio, *downsample_ratio, cv::INTER_LINEAR);
	std::cout << "[DEBUG] in_image_rsz size: " << in_image_rsz.cols << "x" << in_image_rsz.rows << std::endl;

	// 2. Add padding
	cv::Mat in_image_pad;
	int pad_bottom = model_input_size.height - in_image_rsz.rows;
	int pad_right = model_input_size.width - in_image_rsz.cols;
	cv::copyMakeBorder(in_image_rsz, in_image_pad, 0, pad_bottom, 0, pad_right,
		cv::BORDER_CONSTANT, cv::Scalar(114, 114, 114));

	// 3. Run inferences
	int status = infernet[tid]->infer_FD_yolo_landscape(output_tensors, in_image_pad);

	if (status < 0)
		return -1;

	return 1;
}

int Algo::detect_yolo_portrait(std::vector<cv::Mat>& output_tensors, float* downsample_ratio,
	const cv::Mat& in_image, const cv::Size& model_input_size, int tid) {

	std::cout << "[DEBUG] detect_yolo_portrait called" << std::endl;
	std::cout << "[DEBUG] in_image size: " << in_image.cols << "x" << in_image.rows << std::endl;
	std::cout << "[DEBUG] model_input_size: " << model_input_size.width << "x" << model_input_size.height << std::endl;

	// 1. Image preprocessing for portrait
	cv::Mat in_image_rsz;
	*downsample_ratio = std::min((float)model_input_size.width / in_image.cols, (float)model_input_size.height / in_image.rows);
	std::cout << "[DEBUG] downsample_ratio: " << *downsample_ratio << std::endl;
	
	cv::resize(in_image, in_image_rsz, cv::Size(), *downsample_ratio, *downsample_ratio, cv::INTER_LINEAR);
	std::cout << "[DEBUG] in_image_rsz size: " << in_image_rsz.cols << "x" << in_image_rsz.rows << std::endl;

	// 2. Add padding
	cv::Mat in_image_pad;
	int pad_bottom = model_input_size.height - in_image_rsz.rows;
	int pad_right = model_input_size.width - in_image_rsz.cols;
	cv::copyMakeBorder(in_image_rsz, in_image_pad, 0, pad_bottom, 0, pad_right,
		cv::BORDER_CONSTANT, cv::Scalar(114, 114, 114));

	// 3. Run inferences
	int status = infernet[tid]->infer_FD_yolo_landscape(output_tensors, in_image_pad);

	if (status < 0)
		return -1;

	return 1;
}

int Algo::detect_yolo_postprocess(std::vector<cv::Mat>& warp_images, std::vector<float>& ldm, std::vector<float>& bbox, std::vector<float>& score,
	std::vector<cv::Mat> output_tensors, float downsample_ratio, cv::Mat& in_image, const cv::Size& model_input_size) {

	std::cout << "[DEBUG] detect_yolo_postprocess called" << std::endl;
	std::cout << "[DEBUG] postprocess model_input_size: " << model_input_size.width << "x" << model_input_size.height << std::endl;

	// 4. 후처리 준비
	const int num_outputs = 3;
	std::vector<float> filterBoxes;
	std::vector<float> objProbs;
	std::vector<int> classId;
	int validCount = 0;
	int stride = 0;
	int grid_h = 0;
	int grid_w = 0;
	int index = 0;
	// cv::Size model_input_size(640, 384); // YOLO input size (WxH)
	// cv::Size model_input_size(640, 640); // YOLO input size (WxH
	// 5. Processing each output layer
	for (int i = 0; i < num_outputs; i++)
	{
		cv::Mat& output = output_tensors[i];
		float* data = (float*)output.data;

		// Calculate grid size
		int grid_h = output.size[2];
		int grid_w = output.size[3];

		int stride = model_input_size.height / grid_h;

		// Processing
		validCount += process_fp32(data, grid_h, grid_w, stride,
			filterBoxes, objProbs, classId,
			0.6f, 0, 1.0f, index); // default = 0.5f, 0, 1.0f

		index += grid_h * grid_w;
	}

	// 6. Apply NMS - ver.opencv
	std::vector<int> picked;
	if (validCount > 0)
	{
		// std::cout << "validCount : " << validCount <<std::endl;
		// For OpenCV, since it uses cv::Rect instead of float, the values are converted from float to cv::Rect and stored separately in a temporary vector.
		std::vector<cv::Rect> filterRects;
		for (int i = 0; i < objProbs.size(); i++)
		{
			cv::Rect tmp_box(
				filterBoxes[5 * i + 0],
				filterBoxes[5 * i + 1],
				filterBoxes[5 * i + 2],
				filterBoxes[5 * i + 3]);

			filterRects.push_back(tmp_box);
		}
		// cv::dnn::NMSBoxes(filterRects, objProbs, 0.5f, 0.1f, picked);
		NMSBoxes(filterRects, objProbs, 0.6f, 0.5f, picked); // default	0.5f, 0.1f
	}

	// 7. Calculate the scaling factor
	float sx = 1.0f / downsample_ratio;
	float sy = 1.0f / downsample_ratio;

	int cnt = 0;

	// 8. Extract the final result
	for (int idx : picked)
	{
		// Bounding Boxes
		float x = filterBoxes[idx * 5 + 0] * sx;
		float y = filterBoxes[idx * 5 + 1] * sy;
		float w = filterBoxes[idx * 5 + 2] * sx;
		float h = filterBoxes[idx * 5 + 3] * sy;
		bbox.insert(bbox.end(), { x, y, w, h });

		// printf("BBOX : %f, %f, %f, %f\n", x, y, w, h);

		// Key points (assuming output tensor index 3)
		int kp_index = static_cast<int>(filterBoxes[idx * 5 + 4]);
		// output_shape[3]의 실제 크기를 동적으로 가져와서 사용
		const int landmark_width = output_tensors[3].size[3]; // [1, 5, 3, 8400]에서 8400 부분
		const int landmark_channels = output_tensors[3].size[2]; // [1, 5, 3, 8400]에서 3 부분
		const int num_landmarks = output_tensors[3].size[1]; // [1, 5, 3, 8400]에서 5 부분
		
		for (int j = 0; j < num_landmarks; j++)
		{
			float kp_x = ((float*)output_tensors[3].data)[j * landmark_channels * landmark_width + 0 * landmark_width + kp_index] * sx;
			float kp_y = ((float*)output_tensors[3].data)[j * landmark_channels * landmark_width + 1 * landmark_width + kp_index] * sy;
			// printf("landmark : %f, %f\n", kp_x, kp_y);
			ldm.insert(ldm.end(), { kp_x, kp_y });
		}

		// confidence
		score.push_back(objProbs[idx]);
	}

	int ldm_size = 10;

	for (int i = 0; i < ldm.size(); i += ldm_size)
	{
		int end = i + ldm_size;
		std::vector<float> tmp_ldm(ldm.begin() + i, ldm.begin() + end);

		cv::Mat warp_image;
		warpImage(warp_image, in_image, tmp_ldm, 2);

		warp_images.push_back(warp_image);
	}

	return picked.empty() ? 0 : 1;
}

////////////////////////////////// multi face detection///////////////////////////////////
int Algo::detectFace1s_multi(std::vector<float>& ldm, std::vector<float>& bbox, std::vector<float>& score,
	const cv::Mat& in_image, int use_mask, bool tracking_option, int tid, std::vector<std::string> vname) {

	if (tracking_option)
	{
		nframe_cnt++;

		for (int i = 0; i < prior_frame_features.size(); i++)
		{
			if ((std::get<2>(prior_frame_features[i]) - std::get<1>(prior_frame_features[i])) >= 3)
				prior_frame_features.erase(prior_frame_features.begin() + i);
			std::get<1>(prior_frame_features[i])++;

		}
	}

	cv::Mat in_image_rsz, img250;

	// tic();
	cv::cvtColor(in_image, in_image_rsz, cv::COLOR_RGB2BGR);
	// toc("FaceCore", "Face Detection", "Convert input color");

	// First stage
	setConfigDetection(0.1f, 0.1f, 0.2f);
	int stat1 = detectFace_multi(ldm, bbox, score, in_image_rsz, 0, tid, 1); // change from use_mask to 0

	// tic();
	if (stat1 < 0) { return -1; }

	return 1;
}

int Algo::detectFace2s_multi(std::vector<float>& ldm, std::vector<float>& bbox, std::vector<float>& score,
	cv::Mat& out_image, const cv::Mat& in_image, std::vector<float>& bbox_in, int use_mask, bool tracking_option,
	int tid, std::vector<std::string>vname) {

	cv::Mat in_image_rsz, img250, img112;
	cv::cvtColor(in_image, in_image_rsz, cv::COLOR_RGB2BGR);

	// warpImage(img250, in_image_rsz, ldm_in, 1);

	// First stage
	setConfigDetection(0.7f, 0.7f, 0.2f);

	int roi[4] = { 0, };

	// case 2 : Expanding up, down, left, and right by half the width of the bounce box.
	roi[0] = bbox_in[0] - (bbox_in[2] * 0.2);
	roi[1] = bbox_in[1] - (bbox_in[2] * 0.2);
	roi[2] = bbox_in[2] + (bbox_in[2] * 0.4);
	roi[3] = bbox_in[3] + (bbox_in[2] * 0.4);

	std::vector<int> roi_in(roi, roi + 4);

	int stat2 = detectFace_multi_crop(ldm, bbox, score, in_image_rsz, roi_in, use_mask, tid);
	// int stat2 = detectFace_multi(ldm, bbox, score, img250, use_mask, tid, 2);

	if (stat2 < 0) { return -1; }

	warpImage(img112, in_image_rsz, ldm, 2);
	// warpImage(img112, img250, ldm, 2);

	cv::cvtColor(img112, out_image, cv::COLOR_BGR2RGB);

	if (tracking_option)
	{
		// extract feature
		std::vector<uchar> out_feat;
		int stat3 = extractFeatureUINT8(out_feat, img112, 0, 0, 0);
		// int stat3 = extractFeatureUINT8(out_feat, out_image, 0, 0, 0);

		if (stat3 < 0)
			return -1;

		// add checking whether feature is matched with prior frame's features
		if (nframe_cnt != 1)
		{
			int stat4 = -1;

			for (auto prior_frame_feature : prior_frame_features)
			{
				int score = matchPair(std::get<0>(prior_frame_feature).data(), out_feat.data(), 512);
				if (score > 80000)
				{
					std::get<0>(prior_frame_feature) = out_feat;
					std::get<2>(prior_frame_feature)++;
					stat4 = 1;
				}
			}

			if (stat4 < 0)
			{
				prior_frame_features.push_back(std::tuple<std::vector<uchar>, int, int>(out_feat, 1, 0));
				return stat4;
			}
		}
		else
		{
			prior_frame_features.push_back(std::tuple<std::vector<uchar>, int, int>(out_feat, 1, 0));
		}
	}
	// cv::cvtColor(img112, out_image, cv::COLOR_BGR2RGB);

	return 1;
}

int Algo::detectFace_multi_crop(std::vector<float>& ldm_out, std::vector<float>& bbox_out, std::vector<float>& score_out,
	const cv::Mat& in_image_ir, std::vector<int>& bbox1_in, int use_mask,
	int tid, std::vector<std::string>vname) {

	cv::Mat img112;

	float max_w = in_image_ir.size().width;
	float max_h = in_image_ir.size().height;

	float x = bbox1_in[0];
	float y = bbox1_in[1];
	float w = bbox1_in[2];
	float h = bbox1_in[3];

	// Make bbox into square
	float offset_x = 0.f, offset_y = 0.f;
	if (w > h) {
		offset_y = (w - h) / 2;
	}
	else {
		offset_x = (h - w) / 2;
	}

	//// Prevent out of boundaries
	//x = std::max(x - offset_x, 0.f);
	//y = std::max(y - offset_y, 0.f);
	//w = std::min(w + offset_x * 2, max_w);
	//h = std::min(h + offset_y * 2, max_h);

	int expand_letf = 0;
	int expand_right = 0;
	int expand_top = 0;
	int expand_bottom = 0;

	// Prevent out of image
	if (x < 0)
	{
		expand_letf = abs(x);
		x = 0;
	}
	if (y < 0)
	{
		expand_top = abs(y);
		y = 0;
	}
	if ((x + w) > in_image_ir.cols)
	{
		expand_right = abs((x + w) - in_image_ir.cols);
	}
	if ((y + h) > in_image_ir.rows)
	{
		expand_bottom = abs((y + h) - in_image_ir.rows);
	}

	cv::Mat img_border;
	cv::copyMakeBorder(in_image_ir, img_border, expand_top, expand_bottom, expand_letf, expand_right, cv::BORDER_CONSTANT);

	// Crop Image to ROI
	cv::Rect roi(x, y, w, h);
	cv::Mat img_roi = img_border(roi).clone();

	//cv::Mat img_roi = in_image_ir(roi).clone();

	float downsample_ratio = std::max(img_roi.rows / (float)250, img_roi.cols / (float)250);
	int downsample_h = img_roi.rows / downsample_ratio;
	int downsample_w = img_roi.cols / downsample_ratio;

	// Resize to 250x250 for detection
	cv::resize(img_roi, img_roi, cv::Size(downsample_w, downsample_h));

	// Detection usings stage 2
	int stat = detectFace_multi(ldm_out, bbox_out, score_out, img_roi, use_mask, tid, 2);
	if (stat < 0) {
		return -1;
	}

	bbox_out[0] = ((bbox_out.at(0) / downsample_w * w) + x - expand_letf);
	bbox_out[1] = ((bbox_out.at(1) / downsample_h * h) + y - expand_top);
	bbox_out[2] = (bbox_out.at(2) / downsample_w * w - expand_letf);
	bbox_out[3] = (bbox_out.at(3) / downsample_h * h - expand_top);

	for (int i = 0; i < 10; i += 2) {
		ldm_out[i] = (ldm_out[i] / downsample_w * w) + x - expand_letf;
		ldm_out[i + 1] = (ldm_out[i + 1] / downsample_h * h) + y - expand_top;
	}

	return 1;
}

int Algo::detectFace_multi(std::vector<float>& ldm, std::vector<float>& bbox, std::vector<float>& score,
	const cv::Mat& in_image, int use_mask, int tid, int stage,
	std::vector<std::string> vname) {

	int status;
	cv::Mat in_image_rsz;

	// tic();

	// If stage 1, resize half
	if (stage == 1) {
		if (in_image.rows >= in_image.cols)
		{
			float downsample_ratio = std::max(in_image.rows / (float)640, in_image.cols / (float)384);
			int downsample_h = in_image.rows / downsample_ratio;
			int downsample_w = in_image.cols / downsample_ratio;
			cv::resize(in_image, in_image_rsz, cv::Size(downsample_w, downsample_h));
		}
		else
		{
			float downsample_ratio = std::max(in_image.rows / (float)384, in_image.cols / (float)640);
			int downsample_h = in_image.rows / downsample_ratio;
			int downsample_w = in_image.cols / downsample_ratio;
			cv::resize(in_image, in_image_rsz, cv::Size(downsample_w, downsample_h));
		}
	}
	else {
		in_image_rsz = in_image;
	}

	// toc("FaceCore", "Face Detection", "resize input");

	// tic();
	// Make FD network handle dynamic input
	// All input size is possible (new)
	//std::vector<int> sz_and_off = getDynamicInputSize(in_image_rsz.size());
	//cv::Size dyn_fd_sz = { sz_and_off[1], sz_and_off[0] }; // W and H
	cv::Size dyn_fd_sz;
	if (stage == 1)
		if (in_image.rows >= in_image.cols)
			dyn_fd_sz = { 384 , 640 };
		else
			dyn_fd_sz = { 640, 384 };
	else
		dyn_fd_sz = { 256, 256 };

	setConfigFDSize(dyn_fd_sz); // set new size for PriorBox
	// toc("FaceCore", "Face Detection", "set dynamic size");

	// [Optimization]
	// If pbox has been init before && current-prev image size are same
	// skip making prior box ----> save time
	bool skip_prior_box = false;
	if (stage == 1) {
		if (!_prior_box.empty() && (dyn_fd_sz == _fd_size_prev)) {
			skip_prior_box = true;
		}
		_fd_size_prev = dyn_fd_sz;
	}
	// if stage 2, init one time only
	if (stage == 2) {
		skip_prior_box = !_prior_box2.empty();
	}

	// tic();
	// Add padding to input image
	//int bot = sz_and_off[2];
	//int right = sz_and_off[3];
	int bot = dyn_fd_sz.height - in_image_rsz.rows;
	int right = dyn_fd_sz.width - in_image_rsz.cols;
	cv::Mat in_image_pad;
	cv::copyMakeBorder(in_image_rsz, in_image_pad, 0, bot, 0, right, cv::BORDER_CONSTANT);
	// toc("FaceCore", "Face Detection", "copy image border");

	// Inference Network
	std::vector<cv::Mat> out_vtensor = std::vector<cv::Mat>(3);
	infernet[tid]->setConfigFDSize(dyn_fd_sz); // set new size for FD-Net

	if (stage == 2) {

		status = infernet[tid]->infer_FD2(out_vtensor, in_image_pad, use_mask);
	}
	else {


		if (in_image.rows >= in_image.cols) {
			status = infernet[tid]->infer_FD(out_vtensor, in_image_pad, use_mask);

		}
		else {
			status = infernet[tid]->infer_FD_landscape(out_vtensor, in_image_pad, use_mask);

		}
	}

	if (status < 0)
	{
		return -1;
	}

	// Post processing
	// tic();
	if (!skip_prior_box) {
		if (stage == 2) { makePriorBox(_prior_box2); }
		else { makePriorBox(_prior_box); }
	}

	// toc("FaceCore", "Face Detection", "make prior box");

	// tic();
	status = postProcOutput_multi(ldm, bbox, score, out_vtensor, in_image_rsz, stage);

	if (status < 0)
	{
		return -1;
	}

	// tic();
	// Fix scaling	
	float sx = (float)in_image.cols / in_image_rsz.cols;
	float sy = (float)in_image.rows / in_image_rsz.rows;

	for (int i = 0; i < ldm.size(); i += 2) {
		ldm[i] *= sx;
		ldm[i + 1] *= sy;
	}

	for (int i = 0; i < bbox.size(); i += 2)
	{
		bbox[i] *= sx;
		bbox[i + 1] *= sy;
	}

	// toc("FaceCore", "Face Detection", "rescale landmark");

	return 1;
}

float Algo::check_intersection_two_bboxes(const cv::Rect first, const cv::Rect second)
{
	cv::Rect intersection_rect = first & second;

	if (first.area() > second.area())
		return intersection_rect.area() / (float)second.area();
	else
		return intersection_rect.area() / (float)first.area();
}

int Algo::postProcOutput_multi(std::vector<float>& ldm_out, std::vector<float>& bbox_out, std::vector<float>& scores,
	std::vector<cv::Mat>& net_output,
	cv::Mat in_image, int stage) {

	int bbox_sz = 4;
	int ldm_sz = 10;

	// Network inference output
	auto mat_box = net_output[0];
	auto mat_cls = net_output[1];
	auto mat_ldm = net_output[2];
	auto total_detection = mat_box.size[1];

	// Vector -> holding temporary value
	std::vector<int> keep_idx;
	std::vector<cv::Rect> m_bboxes;
	std::vector<float> m_scores;
	std::vector<std::vector<float>> m_landms;

	std::vector<float> bbox(bbox_sz, 0);
	std::vector<float> bbox_in(bbox_sz, 0);

	std::vector<float> ldm(ldm_sz, 0);
	std::vector<float> ldm_in(ldm_sz, 0);

	std::vector<float> temp_pbox(4);


	// Post Process Output
	for (int i = 0; i < total_detection; ++i) {
		// mat_cls--> (batch, idx, face/non-face)
		// class dim 0 is non face, dim 1 is face
		float score = mat_cls.at<float>(0, i, 1);

		if (score >= _score_thres) {

			// Get the prior box
			// Prior size is (total_detection, 4)
			// in single dim, therefore use 4 stride
			for (int pb = 0; pb < 4; pb++) {
				if (stage == 2) {
					temp_pbox[pb] = _prior_box2[i * 4 + pb];
				}
				else {
					temp_pbox[pb] = _prior_box[i * 4 + pb];
				}
			}

			// Push Scores
			m_scores.push_back(score);

			// Push Bounding Boxes
			// box [x, y, w, h]
			for (int j = 0; j < bbox_sz; ++j) {
				bbox_in[j] = mat_box.at<float>(0, i, j);
			}
			decodeSingleBoxFast(bbox, bbox_in, temp_pbox);
			cv::Rect rect(bbox[0], bbox[1], bbox[2] - bbox[0], bbox[3] - bbox[1]);
			m_bboxes.push_back(rect);

			// Push Landmarks
			for (int j = 0; j < ldm_sz; ++j) {
				ldm_in[j] = mat_ldm.at<float>(0, i, j);
			}
			decodeSingleLdmFast(ldm, ldm_in, temp_pbox);
			m_landms.push_back(ldm);
		}
	}

	// Run Non-max-supression
	// Store good detection in `keep_idx`
	cv::dnn::NMSBoxes(m_bboxes, m_scores, _nms_score_thres, _nms_iou_thres, keep_idx);

	if (keep_idx.size() <= 0) {
		// Detection not found
		return -1;
	}

	// Select top K detection
	std::vector<cv::Rect> top_bboxes;
	std::vector<std::vector<float>> top_landms;
	std::vector<float> top_scores;

	if (stage == 2)
	{
		for (int k = 0; k < keep_idx.size(); ++k) {
			int idx = keep_idx[k];

			top_bboxes.push_back(m_bboxes[idx]);
			top_landms.push_back(m_landms[idx]);
			top_scores.push_back(m_scores[idx]);

			//if (k > _top_k_det) { break; }
		}
		scores.push_back(top_scores[0]);
		ldm_out = top_landms[0];
		bbox_out.push_back(top_bboxes[0].x);
		bbox_out.push_back(top_bboxes[0].y);
		bbox_out.push_back(top_bboxes[0].width);
		bbox_out.push_back(top_bboxes[0].height);
	}

	else {
		for (int k = 0; k < keep_idx.size(); ++k) {
			int idx = keep_idx[k];

			if (!top_bboxes.empty())
			{
				bool intersected = false;
				for (auto top_bbox : top_bboxes)
				{
					float intersection_ratio = check_intersection_two_bboxes(top_bbox, m_bboxes[idx]);
					if (intersection_ratio > 0.5)
						intersected = true;
				}
				if (!intersected)
				{
					top_bboxes.push_back(m_bboxes[idx]);
					top_landms.push_back(m_landms[idx]);
					top_scores.push_back(m_scores[idx]);
				}
			}
			else
			{
				top_bboxes.push_back(m_bboxes[idx]);
				top_landms.push_back(m_landms[idx]);
				top_scores.push_back(m_scores[idx]);
			}
		}

		// for (int k = 0; k < keep_idx.size(); ++k) {
		// 	int idx = keep_idx[k];

		// 	top_bboxes.push_back(m_bboxes[idx]);
		// 	top_landms.push_back(m_landms[idx]);
		// 	top_scores.push_back(m_scores[idx]);
		// }


		std::vector<std::size_t> best_idx_order(top_scores.size());
		std::iota(std::begin(best_idx_order), std::end(best_idx_order), 0);

		if (_multi_face_sorting_type == 1)
		{
			//auto comparator = [&](const cv::Rect& b1, const cv::Rect& b2) {
			//	return b1.area() > b2.area();
			//};

			std::sort(std::begin(best_idx_order), std::end(best_idx_order),
				[&](const auto& lhs, const auto& rhs)
				{
					return top_bboxes[lhs].area() > top_bboxes[rhs].area();
				}
			);
		}

		else if (_multi_face_sorting_type == 2)
		{
			//auto comparator = [&](const cv::Rect& b1, const cv::Rect& b2) {
			//	return ((pow((b1.x + b1.width / 2 - in_image.cols / 2), 2)) + (pow((b1.y + b1.height / 2 - in_image.rows / 2), 2)))
			//< ((pow((b2.x + b2.width / 2 - in_image.cols / 2), 2)) + (pow((b2.y + b2.height / 2 - in_image.rows / 2), 2)));
			//};

			std::sort(std::begin(best_idx_order), std::end(best_idx_order),
				[&](const auto& lhs, const auto& rhs)
				{
					return ((pow((top_bboxes[lhs].x + top_bboxes[lhs].width / 2 - in_image.cols / 2), 2)) + (pow((top_bboxes[lhs].y + top_bboxes[lhs].height / 2 - in_image.rows / 2), 2)))
						< ((pow((top_bboxes[rhs].x + top_bboxes[rhs].width / 2 - in_image.cols / 2), 2)) + (pow((top_bboxes[rhs].y + top_bboxes[rhs].height / 2 - in_image.rows / 2), 2)));
				}
			);
		}

		int cnt = 0;

		for (auto idx : best_idx_order)
		{
			scores.push_back(top_scores[idx]);

			bbox_out.push_back(top_bboxes[idx].x);
			bbox_out.push_back(top_bboxes[idx].y);
			bbox_out.push_back(top_bboxes[idx].width);
			bbox_out.push_back(top_bboxes[idx].height);

			for (auto ldm_point : top_landms[idx])
				ldm_out.push_back(ldm_point);

			if (cnt >= _top_k_det - 1) { break; }

			cnt++;

		}
	}
	if (bbox_out.size() == 0)
		return -1;

	return 1;
}
/////////////////////////////////////////////////////////////////////////////////////////


#define pair std::pair<int, int>
#define tuple std::tuple<int, int, int>

// Function to find the line given two points
tuple lineFromPoints(pair P, pair Q)
{
	tuple temp;
	std::get<0>(temp) = Q.first - P.first;
	std::get<1>(temp) = P.second - Q.second;
	std::get<2>(temp) = std::get<0>(temp) * (P.second) + std::get<1>(temp) * (P.first);
	return temp;
	// std::get<0>(temp) * x + std::get<1>(temp) * y = std::get<2>(temp)
}

template <typename T> int sgn(T val) {
	return (T(0) < val) - (val < T(0));
}

int Algo::estimateImageQuality(
	IQ& iq,
	const cv::Mat& warp_image) {

	if (warp_image.empty())
	{
		return -1;
	}

	// estimate Luminance.

	// ldm (x,y) //
	// 0,1   2,3
	//    4,5
	// 6,7   8,9

	std::vector<float> ldm = {
		(float)40.079243, (float)53.47081,
		(float)74.3428, (float)53.505585,
		(float)56.987507, (float)72.96772,
		(float)41.978138, (float)88.09662,
		(float)70.46561, (float)88.34639
	};

	pair eyel = std::make_pair(ldm[1], ldm[0]);
	pair eyer = std::make_pair(ldm[3], ldm[2]);
	pair nose = std::make_pair(ldm[5], ldm[4]);
	pair mouthl = std::make_pair(ldm[7], ldm[6]);
	pair mouthr = std::make_pair(ldm[9], ldm[8]);

	// pdd (y, x) //
	// eyel      eyer
	//      nose
	// mouthl   mouthr

	tic();

	tuple eyel_eyer = lineFromPoints(eyel, eyer);
	tuple eyel_nose = lineFromPoints(eyel, nose);
	tuple nose_eyer = lineFromPoints(nose, eyer);

	tuple eyel_mouthl = lineFromPoints(eyel, mouthl);
	tuple mouthl_nose = lineFromPoints(mouthl, nose);
	tuple nose_mouthr = lineFromPoints(nose, mouthr);
	tuple mouthl_mouthr = lineFromPoints(mouthl, mouthr);
	tuple mouthr_eyer = lineFromPoints(mouthr, eyer);
	//#ifdef _DEBUG
	//	logging.debug("Divide face into 4 parts");
	//#endif
	toc("FaceCore", "IQ", "create line");

	cv::Mat warp_image_gray;

	tic();
	cv::cvtColor(warp_image, warp_image_gray, cv::COLOR_RGB2GRAY);
	//#ifdef _DEBUG
	//	logging.debug("Convert image to gray");
	//#endif
	toc("FaceCore", "IQ", "convert gray");

	struct iq_for_block {
		float y_avg;
		int cnt;
	};

	tic();
	int x_start = 100000;	int x_end = 0;
	int y_start = 100000;	int y_end = 0;
	for (int i = 0; i < 10; i += 2) {
		x_start = x_start > ldm[i] ? ldm[i] : x_start;
		y_start = y_start > ldm[i + 1] ? ldm[i + 1] : y_start;

		x_end = x_end < ldm[i] ? ldm[i] : x_end;
		y_end = y_end < ldm[i + 1] ? ldm[i + 1] : y_end;
	}
	//#ifdef _DEBUG
	//	logging.debug("Calculate valid area in face");
	//#endif
	toc("FaceCore", "IQ", "calculate face range");

	iq_for_block up_iq = { 0, };
	iq_for_block down_iq = { 0, };
	iq_for_block left_iq = { 0, };
	iq_for_block right_iq = { 0, };

	tic();
	for (int i = y_start; i < y_end; i += 1) {
		for (int j = x_start; j < x_end; j += 1) {
			// UP
			if (std::get<0>(eyel_eyer) * j + std::get<1>(eyel_eyer) * i - std::get<2>(eyel_eyer) < 0 &&
				std::get<0>(eyel_nose) * j + std::get<1>(eyel_nose) * i - std::get<2>(eyel_nose) > 0 &&
				std::get<0>(nose_eyer) * j + std::get<1>(nose_eyer) * i - std::get<2>(nose_eyer) > 0) {
				//in_image_display.at<cv::Vec3b>(i, j)[0] = 128;
				//in_image_display.at<cv::Vec3b>(i, j)[1] = 128;
				//in_image_display.at<cv::Vec3b>(i, j)[2] = 128;

				up_iq.cnt++;
				up_iq.y_avg += warp_image_gray.at<uint8_t>(i, j);
			}

			// DOWN
			if (std::get<0>(mouthl_mouthr) * j + std::get<1>(mouthl_mouthr) * i - std::get<2>(mouthl_mouthr) > 0 &&
				std::get<0>(mouthl_nose) * j + std::get<1>(mouthl_nose) * i - std::get<2>(mouthl_nose) < 0 &&
				std::get<0>(nose_mouthr) * j + std::get<1>(nose_mouthr) * i - std::get<2>(nose_mouthr) < 0) {
				//in_image_display.at<cv::Vec3b>(i, j)[0] = 192;
				//in_image_display.at<cv::Vec3b>(i, j)[1] = 192;
				//in_image_display.at<cv::Vec3b>(i, j)[2] = 192;

				down_iq.cnt++;
				down_iq.y_avg += warp_image_gray.at<uint8_t>(i, j);
			}

			// LEFT
			int slop = std::get<1>(eyel_mouthl) == 0 ? 1 : sgn(std::get<0>(eyel_mouthl) * std::get<1>(eyel_mouthl));
			if (std::get<0>(eyel_nose) * j + std::get<1>(eyel_nose) * i - std::get<2>(eyel_nose) < 0 &&
				std::get<0>(mouthl_nose) * j + std::get<1>(mouthl_nose) * i - std::get<2>(mouthl_nose) > 0 &&
				slop * (std::get<0>(eyel_mouthl) * j + std::get<1>(eyel_mouthl) * i - std::get<2>(eyel_mouthl) > 0)) {
				//in_image_display.at<cv::Vec3b>(i, j)[0] = 0;
				//in_image_display.at<cv::Vec3b>(i, j)[1] = 0;
				//in_image_display.at<cv::Vec3b>(i, j)[2] = 0;

				left_iq.cnt++;
				left_iq.y_avg += warp_image_gray.at<uint8_t>(i, j);
			}

			// RIGHT
			slop = std::get<1>(mouthr_eyer) == 0 ? 1 : sgn(std::get<0>(mouthr_eyer) * std::get<1>(mouthr_eyer));
			if (std::get<0>(nose_eyer) * j + std::get<1>(nose_eyer) * i - std::get<2>(nose_eyer) < 0 &&
				std::get<0>(nose_mouthr) * j + std::get<1>(nose_mouthr) * i - std::get<2>(nose_mouthr) > 0 &&
				slop * (std::get<0>(mouthr_eyer) * j + std::get<1>(mouthr_eyer) * i - std::get<2>(mouthr_eyer) > 0)) {
				//in_image_display.at<cv::Vec3b>(i, j)[0] = 64;
				//in_image_display.at<cv::Vec3b>(i, j)[1] = 64;
				//in_image_display.at<cv::Vec3b>(i, j)[2] = 64;

				right_iq.cnt++;
				right_iq.y_avg += warp_image_gray.at<uint8_t>(i, j);
			}
		}
	}
	//#ifdef _DEBUG
	//	logging.debug("Calculate Luminance");
	//#endif
	toc("FaceCore", "IQ", "calcuate luminance");


	up_iq.y_avg = up_iq.y_avg / (up_iq.cnt == 0 ? 1 : up_iq.cnt);
	down_iq.y_avg = down_iq.y_avg / (down_iq.cnt == 0 ? 1 : down_iq.cnt);
	left_iq.y_avg = left_iq.y_avg / (left_iq.cnt == 0 ? 1 : left_iq.cnt);
	right_iq.y_avg = right_iq.y_avg / (right_iq.cnt == 0 ? 1 : right_iq.cnt);

	//int face_area = up_iq.cnt + down_iq.cnt + left_iq.cnt + right_iq.cnt;
	//int face_y_avg = (up_iq.y_avg + down_iq.y_avg + left_iq.y_avg + right_iq.y_avg) / 4;
	//int face_cb_avg = (up_iq.y_avg + down_iq.y_avg + left_iq.y_avg + right_iq.y_avg) / 4;
	//int face_cr_avg = (up_iq.y_avg + down_iq.y_avg + left_iq.y_avg + right_iq.y_avg) / 4;

	int face_area = up_iq.cnt + down_iq.cnt + left_iq.cnt + right_iq.cnt;
	float face_y_avg = (up_iq.y_avg + down_iq.y_avg + left_iq.y_avg + right_iq.y_avg) / 4;

	tic();

	//
	//iq.area[0] = face_area;
	//iq.area[1] = up_iq.cnt;
	//iq.area[2] = down_iq.cnt;
	//iq.area[3] = left_iq.cnt;
	//iq.area[4] = right_iq.cnt;

	iq.y_avg[0] = face_y_avg;
	iq.y_avg[1] = up_iq.y_avg;
	iq.y_avg[2] = down_iq.y_avg;
	iq.y_avg[3] = left_iq.y_avg;
	iq.y_avg[4] = right_iq.y_avg;

	// debug
	if (_verbose == true) {
#ifdef __ANDROID__
		// TBD
#else

		// display for debug
		cv::Mat in_image_display = warp_image.clone();
		//cv::resize(in_image_display, in_image_display, cv::Size(320, 640));

		cv::line(in_image_display, cv::Point(ldm[0], ldm[1]), cv::Point(ldm[2], ldm[3]), cv::Scalar(0, 0, 255), 2);
		cv::line(in_image_display, cv::Point(ldm[0], ldm[1]), cv::Point(ldm[4], ldm[5]), cv::Scalar(0, 0, 255), 2);
		cv::line(in_image_display, cv::Point(ldm[4], ldm[5]), cv::Point(ldm[2], ldm[3]), cv::Scalar(0, 0, 255), 2);
		cv::line(in_image_display, cv::Point(ldm[0], ldm[1]), cv::Point(ldm[6], ldm[7]), cv::Scalar(0, 0, 255), 2);
		cv::line(in_image_display, cv::Point(ldm[6], ldm[7]), cv::Point(ldm[4], ldm[5]), cv::Scalar(0, 0, 255), 2);
		cv::line(in_image_display, cv::Point(ldm[4], ldm[5]), cv::Point(ldm[8], ldm[9]), cv::Scalar(0, 0, 255), 2);
		cv::line(in_image_display, cv::Point(ldm[6], ldm[7]), cv::Point(ldm[8], ldm[9]), cv::Scalar(0, 0, 255), 2);
		cv::line(in_image_display, cv::Point(ldm[8], ldm[9]), cv::Point(ldm[2], ldm[3]), cv::Scalar(0, 0, 255), 2);

		// std::cout << std::endl;
		/*std::cout << "area = " << iq.area[0] << "," << iq.area[1] << "," << iq.area[2] << "," << iq.area[3] << "," << iq.area[4] << std::endl;*/
		//std::cout << "y_avg = " << iq.y_avg[0] << "," << iq.y_avg[1] << "," << iq.y_avg[2] << "," << iq.y_avg[3] << "," << iq.y_avg[4] << std::endl;

		/*cv::imshow("[in] landmark display", in_image_display);
		cv::waitKey(0);*/
		//cv::imwrite("./draw_landmark_point.jpg", in_image_display);
#endif // __ANDROID__
	}

	if (warp_image.empty())
	{
		return -1;
	}

	tic();
	cv::Mat fImage;
	warp_image_gray.convertTo(fImage, CV_32F);
	//#ifdef _DEBUG
	//	logging.debug("Convert data type of image to float 32");
	//#endif
	toc("FaceCore", "IQ", "convert float");

	tic();
	// FFT
	cv::Mat fourierTransform;
	cv::dft(fImage, fourierTransform, cv::DFT_SCALE | cv::DFT_COMPLEX_OUTPUT);
	//#ifdef _DEBUG
	//	logging.debug("Calculate Discrete fourier transform");
	//#endif
	toc("FaceCore", "IQ", "calcuate dft");

	tic();
	cv::Mat planes[2];
	cv::split(fourierTransform, planes);
	//#ifdef _DEBUG
	//	logging.debug("Split real, complex pats");
	//#endif
	toc("FaceCore", "IQ", "split real, complex");

	tic();
	cv::magnitude(planes[0], planes[1], planes[0]);// planes[0] = magnitude
	cv::Mat magI = planes[0];
	//#ifdef _DEBUG
	//	logging.debug("Calculate magnitude");
	//#endif
	toc("FaceCore", "IQ", "calcuate magnitude");

	tic();
	magI += cv::Scalar::all(1);
	cv::log(magI, magI);
	magI = magI(cv::Rect(0, 0, magI.cols & -2, magI.rows & -2));
	int cx = magI.cols / 2;
	int cy = magI.rows / 2;
	cv::Mat q0(magI, cv::Rect(0, 0, cx, cy));   // Top-Left - Create a ROI per quadrant
	cv::Mat q1(magI, cv::Rect(cx, 0, cx, cy));  // Top-Right
	cv::Mat q2(magI, cv::Rect(0, cy, cx, cy));  // Bottom-Left
	cv::Mat q3(magI, cv::Rect(cx, cy, cx, cy)); // Bottom-Right
	cv::Mat tmp;                           // swap quadrants (Top-Left with Bottom-Right)
	q0.copyTo(tmp);
	q3.copyTo(q0);
	tmp.copyTo(q3);
	q1.copyTo(tmp);                    // swap quadrant (Top-Right with Bottom-Left)
	q2.copyTo(q1);
	tmp.copyTo(q2);
	//#ifdef _DEBUG
	//	logging.debug("Swap Quadrants");
	//#endif
	toc("FaceCore", "IQ", "swap quadrants");

	tic();
	cv::Mat gray;
	magI.convertTo(gray, CV_8U, 255);
	//#ifdef _DEBUG
	//	logging.debug("Convert image to 8U");
	//#endif
	toc("FaceCore", "IQ", "covert image");

	float sum_in = 0;
	int pix_count_in = 0;

	float sum_out = 0;
	int pix_count_out = 0;

	tic();
	for (int i = 0; i < gray.rows; i++)
	{
		for (int j = 0; j < gray.cols; j++)
		{
			float dist = sqrt(pow((i - cy), 2) + pow((j - cx), 2));

			if (5.6 <= dist && dist <= 16.8)
			{
				sum_in += gray.at<uchar>(i, j);
				pix_count_in += 1;
			}
			if (16.8 <= dist && dist <= 28)
			{
				sum_out += gray.at<uchar>(i, j);
				pix_count_out += 1;
			}
		}
	}
	//#ifdef _DEBUG
	//	logging.debug("Calculate blurness");
	//#endif
	toc("FaceCore", "IQ", "calculate blurness");

	float blur_in = sum_in / std::max((double)pix_count_in, 1e-12);
	float blur_out = sum_out / std::max((double)pix_count_out, 1e-12);

	iq.blurness = (int)((blur_out / blur_in) * 100);

	//float sum = 0;
	//int pix_count = 0;

	//tic();
	//for (int i = 0; i < gray.rows; i++)
	//{
	//	for (int j = 0; j < gray.cols; j++)
	//	{
	//		float dist = sqrt(pow((i - cx), 2) + pow((j - cy), 2));
	//		if (5 <= dist && dist <= 30)
	//		{
	//			sum += gray.at<uchar>(i, j);
	//			pix_count += 1;
	//		}
	//	}
	//}
	//toc("FaceCore", "IQ", "calculate blurness");

	//float blur = sum / std::max((double)pix_count, 1e-12);

	////float blur = sum / pix_count;

	//iq.blurness = (int)blur;

	return 1;
}

int Algo::extractFeatureFLOAT(std::vector<float>& out_feature,
	const cv::Mat& in_image,
	int model_type,
	bool do_flip,
	int tid,
	std::vector<std::string> vname) {

	// Inference Network
	int status = infernet[tid]->infer_FR(out_feature, in_image, model_type, do_flip);

	if (status < 0)
		return -1;

	return 1;
}

int Algo::extractFeatureUINT8(
	std::vector<unsigned char>& out_feature,
	const cv::Mat& in_image,
	int model_type,
	bool do_flip,
	int tid,
	std::vector<std::string>vname) {

	std::vector<float> out_feature_float;

	int status = extractFeatureFLOAT(out_feature_float, in_image, model_type, do_flip, tid, vname);

	if (status < 0)
		return -1;

	for (auto& n : out_feature_float)
		n = ((n + 0.4) / 0.8) * 255;

	out_feature.assign(out_feature_float.begin(), out_feature_float.end());

	return 1;
}

std::vector<float> norm3f(std::vector<float>& vec) {
#if defined(RV1126) || defined(OPENCV) || defined(LINUX_ID)	
	float mag = std::max((double)1e-8f, (double)sqrt(vec[0] * vec[0] + vec[1] * vec[1] + vec[2] * vec[2]));
#else
	float mag = std::max(1e-8f, sqrt(vec[0] * vec[0] + vec[1] * vec[1] + vec[2] * vec[2]));
#endif
	std::vector<float> vec_norm = { vec[0] / mag, vec[1] / mag, vec[2] / mag };
	return vec_norm;
}

std::vector<float> crossprod3f(std::vector<float>& u, std::vector<float>& v) {
	std::vector<float> z = { u[1] * v[2] - u[2] * v[1],
							 u[2] * v[0] - u[0] * v[2],
							 u[0] * v[1] - u[1] * v[0] };
	return z;
}

void rotmat2pose(std::vector<float>& out_vec, std::vector<float>& in_vec) {

	// std::vector<float> in_vec ===> shape (6)
	std::vector<float> xr = { in_vec[0], in_vec[1], in_vec[2] };
	std::vector<float> yr = { in_vec[3], in_vec[4], in_vec[5] };

	auto x = norm3f(xr);
	auto temp_z = crossprod3f(x, yr);
	auto z = norm3f(temp_z);
	auto y = crossprod3f(z, x);

	auto sy = sqrt(x[0] * x[0] + x[1] * x[1]);
	float singular = 0.0f;
	if (sy < 1e-6f) { singular = 1.0f; }

	float nx = std::atan2(y[2], z[2]);
	float ny = std::atan2(-x[2], sy);
	float nz = std::atan2(x[1], x[0]);

	float xs = std::atan2(-z[1], y[1]);
	float ys = std::atan2(-x[2], sy);
	float zs = x[1] * 0;

	float p_eul = nx * (1 - singular) + xs * singular;
	float y_eul = ny * (1 - singular) + ys * singular;
	float r_eul = nz * (1 - singular) + zs * singular;

	float pi = 2 * std::acos(0.0);

	float pitch = p_eul * 180.0f / pi;
	float yaw = y_eul * 180.0f / pi;
	float roll = r_eul * 180.0f / pi;

	out_vec = { yaw, pitch, roll };
}

int Algo::estimatePose(std::vector<float>& out_pose, const cv::Mat& in_image, std::vector<int>& bbox_in, int tid) {

	float max_w = in_image.size().width;
	float max_h = in_image.size().height;

	float x = bbox_in[0];
	float y = bbox_in[1];
	float w = bbox_in[2];
	float h = bbox_in[3];

	// Make bbox into square
	float offset_x = 0.f, offset_y = 0.f;
	if (w > h) {
		offset_y = (w - h) / 2;
	}
	else {
		offset_x = (h - w) / 2;
	}

	// Prevent out of boundaries
	x = std::max(x - offset_x, 0.f);
	y = std::max(y - offset_y, 0.f);
	w = std::min(w + offset_x * 2, max_w);
	h = std::min(h + offset_y * 2, max_h);

	if (x < 0)
	{
		int tmp_x = 0;
		x = 0;
		w = w + abs(tmp_x);
	}
	if (y < 0)
	{
		int tmp_y = 0;
		y = 0;
		h = h + abs(tmp_y);
	}
	if ((x + w) > in_image.cols)
	{
		int tmp_w = w;
		w = w + (in_image.cols - (x + w));
		x = x + (in_image.cols - (x + tmp_w));
	}
	if ((y + h) > in_image.rows)
	{
		int tmp_h = h;
		h = h + (in_image.rows - (y + h));
		y = y + (in_image.rows - (y + tmp_h));
	}

	//Crop Image to ROI
	cv::Rect roi(x, y, w, h);
	cv::Mat img_roi = in_image(roi).clone();

	// Resize to 250x250 for detection
	//cv::resize(img_roi, img_roi, cv::Size(128, 128));
	cv::resize(img_roi, img_roi, cv::Size(112, 112));

	cv::cvtColor(img_roi, img_roi, cv::COLOR_RGB2BGR);

	std::vector<float> temp_out_pose;

	int status = infernet[tid]->infer_POSE(temp_out_pose, img_roi);

	rotmat2pose(out_pose, temp_out_pose);

	//int status = infernet[tid]->infer_POSE(out_pose, img_roi);

	if (status < 0) {
		return -1;
	}

	return 1;
}

#if defined(RV1126)
int Algo::estimateFAS(std::vector<float>& out_fas, const cv::Mat& in_image, std::vector<float> ldm, int tid) {

	cv::Mat out_image;

	cv::cvtColor(in_image, in_image, cv::COLOR_RGB2BGR);

	// cv::cvtColor(in_image, out_image, cv::COLOR_RGB2BGR);

	setConfigWarping(false, 0, 0);
	warpImage(out_image, in_image, ldm, 0);
	setConfigWarping(false, 0, 1);

	cv::Mat out_image_hsv;
	cv::cvtColor(out_image, out_image_hsv, cv::COLOR_BGR2HSV);

	if (_verbose)
	{
		std::string tmp_img_name = "fas_vis_input.jpg";
		std::string save_path = "C:\\Users\\<USER>\\Desktop\\fas_data\\visual\\" + tmp_img_name;
		cv::imwrite(save_path, out_image);
	}

	int status = infernet[tid]->infer_FAS(out_fas, out_image, out_image_hsv);

	if (status < 0) {
		return -1;
	}

	return 1;
}

int Algo::estimateFAS_NIR(std::vector<float>& out_fas, const cv::Mat& in_image, std::vector<float> ldm, int tid) {

	cv::Mat out_image;

	setConfigWarping(false, 0, 0);
	warpImage(out_image, in_image, ldm, 0);
	setConfigWarping(false, 0, 1);

	if (_verbose)
	{
		std::string tmp_img_name = "fas_nir_input.jpg";
		std::string save_path = "/userdata/media/fas_input/" + tmp_img_name;
		cv::imwrite(save_path, out_image);
	}

	int status = infernet[tid]->infer_FAS_NIR(out_fas, out_image);

	if (status < 0) {
		return -1;
	}

	return 1;
}
#else
int Algo::estimateFAS(std::vector<float>& out_fas, const cv::Mat& in_image, std::vector<float> ldm, int tid) {

	cv::Mat out_image;

	cv::cvtColor(in_image, in_image, cv::COLOR_RGB2BGR);

	warpImage(out_image, in_image, ldm, 0);

	cv::Rect center_crop(38, 38, 180, 180);

	out_image = out_image(center_crop);

	//std::string tmp_img_name = tmp_fas_name;
	//std::string save_path = "C:\\Users\\<USER>\\Desktop\\fas_data\\visual\\" + tmp_img_name;
	//cv::imwrite(save_path, out_image);

	int status = infernet[tid]->infer_FAS(out_fas, out_image);

	if (status < 0) {
		return -1;
	}

	return 1;
}
int Algo::estimateFAS_NIR(std::vector<float>& out_fas, const cv::Mat& in_image, std::vector<float> ldm, int tid) {

	cv::Mat out_image;

	warpImage(out_image, in_image, ldm, 0);

	//cv::cvtColor(out_image, out_image, cv::COLOR_RGB2GRAY);

	cv::Rect center_crop(38, 38, 180, 180);

	out_image = out_image(center_crop);

	//std::string tmp_img_name = tmp_fas_name;
	//std::string save_path = "C:\\Users\\<USER>\\Desktop\\fas_data\\nir\\" + tmp_img_name;
	//cv::imwrite(save_path, out_image);

	int status = infernet[tid]->infer_FAS_NIR(out_fas, out_image);

	if (status < 0) {
		return -1;
	}

	return 1;
}
#endif

int Algo::estimateMS(std::vector<cv::Mat>& out_ms, const cv::Mat& in_image, int tid)
{

	cv::Mat in_img = in_image.clone();
	cv::Mat out_image;

	//cv::cvtColor(in_img, in_img, cv::COLOR_BGR2RGB);
	cv::resize(in_img, out_image, cv::Size(100, 100));

	if (_verbose)
	{
		std::string tmp_img_name = "ms_input.jpg";
		std::string save_path = "D:\\suprema_visual\\MS_from_origin_RGB\\" + tmp_img_name + ".jpg";
		cv::imwrite(save_path, out_image);
	}

	int status = infernet[tid]->infer_MS(out_ms, out_image);

	if (status < 0) {
		return -1;
	}

	return 1;
}

int Algo::estimateFAM(int* age, int* gender, int* emotion, int* glass, int* race,
	const cv::Mat& in_image, int tid)
{
	cv::Mat img_rsz;
	cv::resize(in_image, img_rsz, cv::Size(128, 128));

	cv::Rect center_crop(4, 4, 120, 120);
	img_rsz = img_rsz(center_crop);

	std::vector<float*> out_vtensor = std::vector<float*>(5);
	int status = infernet[tid]->infer_FAM(out_vtensor, img_rsz);

	if (status < 0) {
		return -1;
	}

	int res_age = -1;
	int res_gen = -1;
	int res_emo = -1;
	int res_gla = -1;
	int res_race = -1;

	// Post Process
	for (int i = 0; i < 5; i++)
	{
		if (i == 0)
		{
			// age
			for (int j = 0; j < 48; j++)
			{
				if (out_vtensor[i][j] >= 0.5)
					res_age++;
			}
		}
		else if (i == 1)
		{
			// gender
			out_vtensor[i][0] > out_vtensor[i][1] ? res_gen = 0 : res_gen = 1;
		}
		else if (i == 2)
		{
			int max_idx = -1;
			float max_val = 0.0;
			// emotion
			for (int j = 0; j < 7; j++)
			{
				if (out_vtensor[i][j] > max_val)
				{
					max_val = out_vtensor[i][j];
					max_idx = j;
				}
			}
			res_emo = max_idx;
		}
		else if (i == 3)
		{
			// glass
			out_vtensor[i][0] < out_vtensor[i][1] ? res_gla = 1 : res_gla = 0;
		}
		// else if (i == 4)
		// {
		// 	int max_idx = -1;
		// 	float max_val = 0.0;
		// 	// race
		// 	for (int j = 0; j < 3; j++)

		// 	{
		// 		if (out_vtensor[i][j] > max_val)

		// 		{
		// 			max_val = out_vtensor[i][j];
		// 			max_idx = j;
		// 		}
		// 	}
		// 	res_mask = max_idx;
		// }
		else if (i == 4)
		{
			int max_idx = -1;
			float max_val = 0.0;
			// race
			for (int j = 0; j < 4; j++)

			{
				if (out_vtensor[i][j] > max_val)

				{
					max_val = out_vtensor[i][j];
					max_idx = j;
				}
			}
			res_race = max_idx;
		}
		else
			continue;
	}

	*age = res_age + 14;
	*gender = res_gen;
	*emotion = res_emo;
	*glass = res_gla;
	// *mask = res_mask;
	*race = res_race;

	return 1;
}

int Algo::matchPair(unsigned char* feature_1st, unsigned char* feature_2nd, int feature_len) {
	uint32_t dist = 0.0F;
	for (int i = 0; i < feature_len; i++) {
		dist += (feature_1st[i] - feature_2nd[i]) * (feature_1st[i] - feature_2nd[i]);
	}
	return (int)(100000 - dist * ((100000 * 0.8 * 0.8) / (256 * 256 * 4)));
}

int* Algo::matchBatch(void* bpt, unsigned char* feature, int feature_len) {

	tic();

	std::vector<std::vector<unsigned char>>* bpt_cast = (std::vector<std::vector<unsigned char>>*)bpt;

	toc("FaceCore", "Match", "convert batch");

	int numGallery = bpt_cast->size();


	if (numGallery == 0)
		return nullptr;

	tic();
	int* output_array = new int[numGallery];
	toc("FaceCore", "Match", "assign output array");

	int score;

	tic();
	for (int i = 0; i < numGallery; i++)
	{
		score = matchPair(bpt_cast[0][i].data(), feature, feature_len);
		output_array[i] = score;
	}
	toc("FaceCore", "Match", "calculate 1:N");

	return output_array;

}

void Algo::matchBatch_pthread(int* out_score_array_heap, std::vector<std::vector<unsigned char>>* in_batch, unsigned char* in_feature,
	int in_feature_len, int idx_start, int idx_end, int thread_num)
{
	for (int i = idx_start; i < idx_end; ++i) {
		// compute similarity values between two templates. 
		unsigned char* in_batch_feature = reinterpret_cast<unsigned char*>(in_batch[0][i].data());
		int out_score;
		out_score = matchPair(in_batch_feature, in_feature, in_feature_len);
		memcpy(&out_score_array_heap[i], &out_score, sizeof(int));
		//printf("loop%d : %d, %d, %d\n", thread_num, i, out_score, out_score_array_heap[i]);
	}
}

///////////////////////////////////////////////////////////
//                Multi-thread Funtions                  //
// If User's OS is Window, then use Multi-thread Funtions//
// others are Single-thread Funtions                     //
///////////////////////////////////////////////////////////
#ifdef _WIN32
void Algo::detectFace_Pthread(unsigned char** out_image_heap, int* out_image_height, int* out_image_width, int** landmark_array, struct IQ** iq,
	unsigned char** in_image, int* in_image_height, int* in_image_width, int idx_start, int idx_end, int thread_num)
{
	// initialize & assign struct FaceCore_multi 
	bool ret = RETURN_OK;

	for (int i = idx_start; i < idx_end; ++i) {

		// printf("Thread :  %d, image_num : %d\n", thread_num, i);

		// initialize variables.
		const int warped_height = 112, warped_width = 112, warped_channel = 3;
		cv::Mat img_raw(in_image_height[i], in_image_width[i], CV_8UC3, in_image[i]);
		cv::Mat img_warped;
		std::vector<float> ldm_out;
		std::vector<float> ldm1;

		//============================================
		// Set the configuration before FD
		//============================================
		//algo.setConfigDetection(score, nms, nms_iou);
		//algo.setConfigWarping(false, 1, 0);
		//============================================

		// detect face.
		//int ret = detectFace2s(ldm_out, ldm1, img_warped, img_raw, thread_num);

		// estimate image quality.
		if (ret == RETURN_OK) {
			//out_image_heap[i] = (unsigned char*)malloc(warped_height * warped_width * warped_channel * sizeof(unsigned char));
			memcpy(out_image_heap[i], img_warped.data, sizeof(unsigned char) * img_warped.total() * img_warped.elemSize());
			*out_image_height = warped_height;
			*out_image_width = warped_width;
			for (int j = 0; j < 10; j++) {
				landmark_array[i][j] = (int)(ldm_out.at(j) + 0.5);

				/////(x,y)//////
				// 0,1   2,3
				//    4,5
				// 6,7   8,9 
				///////////////
			}

			estimateImageQuality((*iq)[i], img_warped);
		}
		else {	// ret == algo.RETURN_FAIL
			out_image_heap[i] = NULL;
			*out_image_height = 0;
			*out_image_width = 0;
		}
	}
}

void Algo::estimatePose_pthread(float** out_pose_heap, int* out_pose_len,
	unsigned char** in_image, int in_image_height, int in_image_width, int idx_start, int idx_end, int thread_num)
{

	bool status = RETURN_OK;

	for (int i = idx_start; i < idx_end; ++i) {

		// printf("Thread :  %d, image_num : %d\n", thread_num, i);

		// fcore_extract
		cv::Mat img_warped(in_image_height, in_image_width, CV_8UC3, in_image[i]);
		std::vector<float> out_pose;

		// extract template from detected face
		//status = estimatePose(out_pose, img_warped, thread_num);

		if (status == RETURN_OK)
		{
			*out_pose_len = out_pose.size();
			std::copy(out_pose.begin(), out_pose.end(), out_pose_heap[i]);
		}
		else
		{
			*out_pose_len = 0;
			out_pose_heap[i] = NULL;
		}
	}
}

void Algo::extractFeatureUINT8_pthread(unsigned char** out_feature_heap, int* out_feature_len,
	unsigned char** in_image, int in_image_height, int in_image_width, int idx_start, int idx_end, int thread_num)
{

	for (int i = idx_start; i < idx_end; ++i) {

		// printf("Thread :  %d, image_num : %d\n", thread_num, i);

		// fcore_extract
		cv::Mat img_warped(in_image_height, in_image_width, CV_8UC3, in_image[i]);
		std::vector<unsigned char> out_feat;
		bool do_flip = true;

		// extract template from detected face
		extractFeatureUINT8(out_feat, img_warped, do_flip, thread_num);

		*out_feature_len = out_feat.size();
		out_feature_heap[i] = (unsigned char*)malloc((*out_feature_len) * sizeof(unsigned char));
		std::copy(out_feat.begin(), out_feat.end(), out_feature_heap[i]);
	}
}

#endif

int Algo::postProcOutput(float* score_out, std::vector<float>& ldm_out, std::vector<float>& bbox_out,
	std::vector<cv::Mat>& net_output,
	cv::Mat in_image, int stage) {

	int bbox_sz = 4;
	int ldm_sz = 10;

	// Network inference output
	auto mat_box = net_output[0];
	auto mat_cls = net_output[1];
	auto mat_ldm = net_output[2];
	auto total_detection = mat_box.size[1];

	// Vector -> holding temporary value
	std::vector<int> keep_idx;
	std::vector<cv::Rect> m_bboxes;
	std::vector<float> m_scores;
	std::vector<std::vector<float>> m_landms;

	std::vector<float> bbox(bbox_sz, 0);
	std::vector<float> bbox_in(bbox_sz, 0);

	std::vector<float> ldm(ldm_sz, 0);
	std::vector<float> ldm_in(ldm_sz, 0);

	std::vector<float> temp_pbox(4);

	float max_score = 0.0;

	// Post Process Output
	for (int i = 0; i < total_detection; ++i) {
		// mat_cls--> (batch, idx, face/non-face)
		// class dim 0 is non face, dim 1 is face
		float score = mat_cls.at<float>(0, i, 1);

		if (max_score < score)
			max_score = score;

		if (score >= _score_thres) {

			// Get the prior box
			// Prior size is (total_detection, 4)
			// in single dim, therefore use 4 stride
			for (int pb = 0; pb < 4; pb++) {
				if (stage == 2) {
					temp_pbox[pb] = _prior_box2[i * 4 + pb];
				}
				else {
					temp_pbox[pb] = _prior_box[i * 4 + pb];
				}
			}

			// Push Scores
			m_scores.push_back(score);

			// Push Bounding Boxes
			// box [x, y, w, h]
			for (int j = 0; j < bbox_sz; ++j) {
				bbox_in[j] = mat_box.at<float>(0, i, j);
			}
			decodeSingleBoxFast(bbox, bbox_in, temp_pbox);
			cv::Rect rect(bbox[0], bbox[1], bbox[2] - bbox[0], bbox[3] - bbox[1]);
			m_bboxes.push_back(rect);

			// Push Landmarks
			for (int j = 0; j < ldm_sz; ++j) {
				ldm_in[j] = mat_ldm.at<float>(0, i, j);
			}
			decodeSingleLdmFast(ldm, ldm_in, temp_pbox);
			m_landms.push_back(ldm);
		}
	}

	// Run Non-max-supression
	// Store good detection in `keep_idx`
	cv::dnn::NMSBoxes(m_bboxes, m_scores, _nms_score_thres, _nms_iou_thres, keep_idx);

	if (keep_idx.size() <= 0) {
		// Detection not found
		return -1;
	}

	// Select top K detection
	std::vector<cv::Rect> top_bboxes;
	std::vector<std::vector<float>> top_landms;
	std::vector<float> top_scores;
	for (int k = 0; k < keep_idx.size(); ++k) {
		int idx = keep_idx[k];
		top_bboxes.push_back(m_bboxes[idx]);
		top_landms.push_back(m_landms[idx]);
		top_scores.push_back(m_scores[idx]);

		if (k > _top_k_det) { break; }
	}

	if (top_scores.size() == 0)
		return -1;

	int best_idx = 0;

	// // Select the largest Bounding box face
	// // from top_bboxes
	// if (stage == 1)
	// {
	// 	if (_face_sorting_type == 1)
	// 	{
	// 		auto comparator = [&](const cv::Rect& b1, const cv::Rect& b2) {
	// 			return b1.area() < b2.area();
	// 		};

	// 		auto result = std::max_element(top_bboxes.begin(), top_bboxes.end(), comparator);
	// 		best_idx = std::distance(top_bboxes.begin(), result);
	// 	}

	// 	//dist_tmp = rect.area - 2.0 * (pow((bbox_cx - ori_cx), 2) + pow((bbox_cy - ori_cy), 2));
	// 	//auto comparator = [&](const cv::Rect& b1, const cv::Rect& b2) {
	// 	//	return (b1.area() - 2.0 *( (pow((b1.x + b1.width/2 - in_image.cols),2)) + (pow((b1.y + b1.height/2 - in_image.rows), 2)))) 
	// 	//		< (b2.area() - 2.0 * ((pow((b2.x + b2.width / 2 - in_image.cols), 2)) + (pow((b2.y + b2.height / 2 - in_image.rows), 2))));
	// 	//};

	// 	else if (_face_sorting_type == 2)
	// 	{
	// 		auto comparator = [&](const cv::Rect& b1, const cv::Rect& b2) {
	// 			return ((pow((b1.x + b1.width / 2 - in_image.cols / 2), 2)) + (pow((b1.y + b1.height / 2 - in_image.rows / 2), 2)))
	// 			> ((pow((b2.x + b2.width / 2 - in_image.cols / 2), 2)) + (pow((b2.y + b2.height / 2 - in_image.rows / 2), 2)));
	// 		};

	// 		auto result = std::max_element(top_bboxes.begin(), top_bboxes.end(), comparator);
	// 		best_idx = std::distance(top_bboxes.begin(), result);
	// 	}
	// }

	// original
	*score_out = top_scores[best_idx];
	ldm_out = top_landms[best_idx];
	bbox_out.push_back(top_bboxes[best_idx].x);
	bbox_out.push_back(top_bboxes[best_idx].y);
	bbox_out.push_back(top_bboxes[best_idx].width);
	bbox_out.push_back(top_bboxes[best_idx].height);
	//*score_out = top_scores[0];
	//ldm_out = top_landms[0];
	//bbox_out.push_back(top_bboxes[0].x);
	//bbox_out.push_back(top_bboxes[0].y);
	//bbox_out.push_back(top_bboxes[0].width);
	//bbox_out.push_back(top_bboxes[0].height);

	//printf("top scores : %lf\n", top_scores[0]);

	return 1;
}

void Algo::makePriorBox(std::vector<float>& pbox) {

	int im_h = _fd_size.height;
	int im_w = _fd_size.width;

	int total_sz = 0;

	std::vector<std::vector<int>> fmap;
	for (auto& st : _steps) {
		fmap.push_back({ im_h / st, im_w / st });
		total_sz += ((im_h / st) * (im_w / st) * 2);
	}

	// Pbox count index
	int pbi = 0;

	// Pbox shape is (total_sz, 4)
	// Make into single dim for faster exec
	pbox.resize(total_sz * 4);


	for (int idx = 0; idx < fmap.size(); idx++) {
		auto msz = _min_size[idx];
		for (int i = 0; i < fmap[idx][0]; i++) {
			for (int j = 0; j < fmap[idx][1]; j++) {
				for (auto& m : msz) {
					auto skx = (float)m / im_w;
					auto sky = (float)m / im_h;
					auto dx = (j + 0.5f) * _steps[idx] / im_w;
					auto dy = (i + 0.5f) * _steps[idx] / im_h;

					pbox[pbi] = dx;
					pbox[pbi + 1] = dy;
					pbox[pbi + 2] = skx;
					pbox[pbi + 3] = sky;
					pbi += 4; // count stride 4
				}
			}
		}
	}

}

void Algo::decodeSingleBoxFast(std::vector<float>& box_out,
	std::vector<float>& box_in, std::vector<float>& prior) {

	// x1, y1, w, h
	box_out[0] = prior[0] + box_in[0] * _var1 * prior[2];
	box_out[1] = prior[1] + box_in[1] * _var1 * prior[3];
	box_out[2] = prior[2] * exp(box_in[2] * _var2);
	box_out[3] = prior[3] * exp(box_in[3] * _var2);

	// x1, y1, x2, y2
	box_out[0] -= box_out[2] / 2;
	box_out[1] -= box_out[3] / 2;
	box_out[2] += box_out[0];
	box_out[3] += box_out[1];

	//normalized to image size
	box_out[0] *= _fd_size.width;  // x1
	box_out[1] *= _fd_size.height; // y1
	box_out[2] *= _fd_size.width;  // x2
	box_out[3] *= _fd_size.height; // y2

}

void Algo::decodeSingleLdmFast(std::vector<float>& ldm_out,
	std::vector<float>& ldm_in, std::vector<float>& prior) {

	for (int i = 0; i < 10; i += 2) {
		ldm_out[i] = (prior[0] + ldm_in[i] * _var1 * prior[2]) * _fd_size.width;
		ldm_out[i + 1] = (prior[1] + ldm_in[i + 1] * _var1 * prior[3]) * _fd_size.height;
	}
}

void Algo::warpImage(cv::Mat& out_image, const cv::Mat& in_image,
	std::vector<float>& landm, int stage) {

	// Reference Landmark
	Eigen::VectorXf ldm_ref(10, 1);
	cv::Size out_sz;

	if (stage == 0) {
#if defined(RV1126)
		ldm_ref << 57.71325684, 76.54121399,
			121.49238586, 75.71583557,
			90.00975037, 112.52455139,
			68.55767822, 145.4400177,
			115.41694641, 144.69500732;
		out_sz = cv::Size(180, 180);
#else
		ldm_ref << 95.71325684, 114.54121399,
			159.49238586, 113.71583557,
			128.00975037, 150.52455139,
			106.55767822, 183.4400177,
			153.41694641, 182.69500732;
		out_sz = cv::Size(256, 256);
#endif
	}
	else if (stage == 1) {
		ldm_ref << 95.71325684, 114.54121399,
			159.49238586, 113.71583557,
			128.00975037, 150.52455139,
			106.55767822, 183.4400177,
			153.41694641, 182.69500732;
		out_sz = cv::Size(250, 250);
	}
	else {
		ldm_ref << 40.079243, 53.47081,
			74.3428, 53.505585,
			56.987507, 72.96772,
			41.978138, 88.09662,
			70.46561, 88.34639;
		out_sz = cv::Size(112, 112);
	}

	// ==================================================
	// TODO : Experimental
	// Handle case where the face pose is 90 degree
	// By finding if the landmark eye location is
	// less than nose location on x axis

	// If 90 degree from left
	if (_fix_alignment) {
		if (landm[2] < landm[4] && landm[8] < landm[4]) {
			ldm_ref[2] = ldm_ref[0];
			ldm_ref[3] = ldm_ref[1];
			ldm_ref[8] = ldm_ref[6];
			ldm_ref[9] = ldm_ref[7];
		}
		// If 90 degree from right
		if (landm[0] > landm[4] && landm[6] > landm[4]) {
			ldm_ref[0] = ldm_ref[2];
			ldm_ref[1] = ldm_ref[3];
			ldm_ref[6] = ldm_ref[8];
			ldm_ref[7] = ldm_ref[9];
		}
	}
	// ==================================================

	Eigen::MatrixXf A(10, 4);
	A << ldm_ref[0], ldm_ref[1], 1.0f, 0.f,
		ldm_ref[2], ldm_ref[3], 1.0f, 0.f,
		ldm_ref[4], ldm_ref[5], 1.0f, 0.f,
		ldm_ref[6], ldm_ref[7], 1.0f, 0.f,
		ldm_ref[8], ldm_ref[9], 1.0f, 0.f,
		ldm_ref[1], -ldm_ref[0], 0.f, 1.f,
		ldm_ref[3], -ldm_ref[2], 0.f, 1.f,
		ldm_ref[5], -ldm_ref[4], 0.f, 1.f,
		ldm_ref[7], -ldm_ref[6], 0.f, 1.f,
		ldm_ref[9], -ldm_ref[8], 0.f, 1.f;

	Eigen::VectorXf ldm_tgt(10, 1);
	ldm_tgt << landm[0], landm[2], landm[4], landm[6], landm[8],
		landm[1], landm[3], landm[5], landm[7], landm[9];

	tic();
	Eigen::VectorXf x = A.bdcSvd(Eigen::ComputeThinU | Eigen::ComputeThinV).solve(ldm_tgt);
	toc("FaceCore", "Face Detection", "SVD");

	Eigen::MatrixXf xt(3, 3);
	xt << x(0), -x(1), 0.f,
		x(1), x(0), 0.f,
		x(2), x(3), 1.0f;

	tic();
	Eigen::MatrixXf xt_inv = xt.inverse();
	toc("FaceCore", "Face Detection", "inverse matrix");

	cv::Mat t_mat(2, 3, CV_32F);
	t_mat.at<float>(0, 0) = xt_inv(0, 0);
	t_mat.at<float>(0, 1) = xt_inv(1, 0);
	t_mat.at<float>(0, 2) = xt_inv(2, 0);
	t_mat.at<float>(1, 0) = xt_inv(0, 1);
	t_mat.at<float>(1, 1) = xt_inv(1, 1);
	t_mat.at<float>(1, 2) = xt_inv(2, 1);

	cv::BorderTypes border_val;
	_border_type == 0 ? border_val = cv::BORDER_CONSTANT : border_val = cv::BORDER_REPLICATE;
	cv::InterpolationFlags warp_val;
	_warp_type == 0 ? warp_val = cv::INTER_LINEAR : warp_val = cv::INTER_CUBIC;

	tic();
	cv::warpAffine(in_image, out_image, t_mat, out_sz, warp_val, border_val);
	toc("FaceCore", "Face Detection", "warp affine");

}

void Algo::adjustImage(cv::Mat& out_image, cv::Mat& in_image,
	float threshold, float gamma, bool roi, std::string color) {

	// Convert to HSV
	cv::Mat img, img_hsv;
	cv::cvtColor(in_image, img_hsv, cv::COLOR_BGR2HSV);

	// Split HSV channel
	std::vector<cv::Mat> hsv_split;
	cv::split(img_hsv, hsv_split);
	cv::Mat luma = hsv_split[2]; // hsv

	// Make the histogram
	std::vector<int> bins, bins_val;
	bins.reserve(26);
	bins_val.reserve(26);

	for (int i = 0; i < 26; i++) {
		bins.push_back(i * 10);
		bins_val.push_back(0);
	}

	// Loop over images ROI
	// Get the value of Luma channel
	int wst = 25;
	int wnd = 100;
	int hst = 40;
	int hnd = 80;

	for (int h = hst; h < hnd; h++) {
		for (int w = wst; w < wnd; w++) {
			auto val = luma.at<uint8_t>(h, w);
			for (int i = 0; i < bins.size(); i++) {
				if (val <= bins[i]) {
					bins_val[i] += 1;
					break;
				}
			}
		}
	}

	// Get the left value from
	// the first 4 bins value
	int total_roi = (hnd - hst) * (wnd - wst);
	float left_val = 0.f;
	for (int i = 0; i < 4; i++) {
		left_val += ((float)bins_val[i] / (float)total_roi);
	}

	// Comput threshold
	if (left_val >= threshold) {
		// Adjust the gamma value
		cv::Mat luma_adj;
		adjustGamma(luma_adj, luma, gamma);

		// Merge back HSV to BGR
		cv::Mat img_adj;
		hsv_split[2] = luma_adj;
		cv::merge(hsv_split, img_adj);
		cv::cvtColor(img_adj, img_adj, cv::COLOR_HSV2BGR);

		// Apply little blur for noise reduction
		cv::GaussianBlur(img_adj, img_adj, cv::Size(3, 3), 0.5);
		out_image = img_adj;
	}
	else {
		out_image = in_image;
	}
}

void Algo::adjustGamma(cv::Mat& out, cv::Mat& in, float gamma) {
	float inv_gamma = 1.0f / gamma;

	std::vector<uint8_t> lutable;
	lutable.reserve(256);

	for (int i = 0; i < 256; i++) {
		float v = float(i) / 255.0f;
		v = pow(v, inv_gamma) * 255;
		lutable.push_back((uint8_t)v);
	}

	cv::LUT(in, lutable, out);

}

float Algo::matching(float max_val, std::vector<float>& templ1, std::vector<float>& templ2) {
	float score = 0.0f;
	float feat_sum = 0.0f;

	for (int i = 0; i < templ1.size(); i++) {
		feat_sum += pow(templ1[i] - templ2[i], 2.0);
	}
	score = max_val - (feat_sum * (max_val / 4));


	return score;
}

float Algo::matching(float max_val, int tmpl_size, float* templ1, float* templ2) {
	float score = 0.0f;
	float feat_sum = 0.0f;

	for (int i = 0; i < tmpl_size; i++) {
		feat_sum += pow(templ1[i] - templ2[i], 2.0);
	}
	score = max_val - (feat_sum * (max_val / 4));


	return score;
}


// IoU Calculation Functions
float Algo::IoU(const cv::Rect2f& a, const cv::Rect2f& b)
{
	float x1 = std::max(a.x, b.x);
	float y1 = std::max(a.y, b.y);
	float x2 = std::min(a.x + a.width, b.x + b.width);
	float y2 = std::min(a.y + a.height, b.y + b.height);

	float inter_width = std::max(0.0f, x2 - x1);
	float inter_height = std::max(0.0f, y2 - y1);
	float inter_area = inter_width * inter_height;
	float union_area = a.area() + b.area() - inter_area;

	return (union_area > 0.0f) ? (inter_area / union_area) : 0.0f;
}

// Implementing NMSBoxes 
void Algo::NMSBoxes(const std::vector<cv::Rect>& bboxes,
	const std::vector<float>& scores,
	float score_threshold,
	float nms_threshold,
	std::vector<int>& indices,
	float eta,
	int top_k)
{
	indices.clear();
	std::vector<int> sorted_indices;

	for (size_t i = 0; i < scores.size(); ++i)
	{
		if (scores[i] > score_threshold)
		{
			sorted_indices.push_back(static_cast<int>(i));
		}
	}

	std::sort(sorted_indices.begin(), sorted_indices.end(),
		[&scores](int i1, int i2)
		{
			return scores[i1] > scores[i2];
		});

	if (top_k > 0 && static_cast<int>(sorted_indices.size()) > top_k)
	{
		sorted_indices.resize(top_k);
	}

	float adaptive_threshold = nms_threshold;

	while (!sorted_indices.empty())
	{
		int idx = sorted_indices.front();
		indices.push_back(idx);
		sorted_indices.erase(sorted_indices.begin());

		std::vector<int> remaining;
		for (int i : sorted_indices)
		{
			float iou = IoU(bboxes[idx], bboxes[i]);
			if (iou <= adaptive_threshold)
			{
				remaining.push_back(i);
			}
		}

		sorted_indices = remaining;

		if (eta < 1.0f && adaptive_threshold > 0.5f)
		{
			adaptive_threshold *= eta;
		}
	}
}


static float unsigmoid(float y)
{
	return -1.0 * logf((1.0 / y) - 1.0);
}

inline float sigmoid(float x)
{
	return static_cast<float>(1.f / (1.f + exp(-x)));
}

void softmax(float* input, int size)
{
	float max_val = input[0];
	for (int i = 1; i < size; ++i)
	{
		if (input[i] > max_val)
		{
			max_val = input[i];
		}
	}

	float sum_exp = 0.0;
	for (int i = 0; i < size; ++i)
	{
		sum_exp += expf(input[i] - max_val);
	}

	for (int i = 0; i < size; ++i)
	{
		input[i] = expf(input[i] - max_val) / sum_exp;
	}
}

int Algo::process_fp32(float* input, int grid_h, int grid_w, int stride,
	std::vector<float>& boxes, std::vector<float>& boxScores, std::vector<int>& classId, float threshold,
	int32_t zp, float scale, int index)
{
	constexpr int input_loc_len = 64;
	constexpr int tensor_len = input_loc_len + OBJ_CLASS_NUM;
	int validCount = 0;
	float thres_fp = unsigmoid(threshold);

	for (int h = 0; h < grid_h; h++)
	{
		for (int w = 0; w < grid_w; w++)
		{
			for (int a = 0; a < OBJ_CLASS_NUM; a++)
			{
				if (input[(input_loc_len + a) * grid_w * grid_h + h * grid_w + w] >= thres_fp)
				{
					float box_conf_f32 = sigmoid(input[(input_loc_len + a) * grid_w * grid_h + h * grid_w + w]);

					std::array<float, input_loc_len> loc;
					for (int i = 0; i < input_loc_len; ++i)
					{
						loc[i] = input[i * grid_w * grid_h + h * grid_w + w];
					}

					for (int i = 0; i < input_loc_len / 16; ++i)
					{
						softmax(&loc[i * 16], 16);
					}

					float xywh_[4] = { 0, 0, 0, 0 };
					float xywh[4] = { 0, 0, 0, 0 };
					for (int dfl = 0; dfl < 16; ++dfl)
					{
						xywh_[0] += loc[dfl] * dfl;
						xywh_[1] += loc[1 * 16 + dfl] * dfl;
						xywh_[2] += loc[2 * 16 + dfl] * dfl;
						xywh_[3] += loc[3 * 16 + dfl] * dfl;
					}

					xywh_[0] = (w + 0.5f) - xywh_[0];
					xywh_[1] = (h + 0.5f) - xywh_[1];
					xywh_[2] = (w + 0.5f) + xywh_[2];
					xywh_[3] = (h + 0.5f) + xywh_[3];
					xywh[0] = ((xywh_[0] + xywh_[2]) / 2) * stride;
					xywh[1] = ((xywh_[1] + xywh_[3]) / 2) * stride;
					xywh[2] = (xywh_[2] - xywh_[0]) * stride;
					xywh[3] = (xywh_[3] - xywh_[1]) * stride;
					xywh[0] = xywh[0] - xywh[2] / 2;
					xywh[1] = xywh[1] - xywh[3] / 2;

					boxes.push_back(xywh[0]);
					boxes.push_back(xywh[1]);
					boxes.push_back(xywh[2]);
					boxes.push_back(xywh[3]);
					boxes.push_back(float(index + (h * grid_w) + w));
					boxScores.push_back(box_conf_f32);
					classId.push_back(a);
					validCount++;
				}
			}
		}
	}

	return validCount;
}

void Algo::tic() {
	// _pt = std::chrono::high_resolution_clock::now();
}

void Algo::toc(std::string tag, std::string part, std::string process) {
	// 	chrono_tpoint_t nd = std::chrono::high_resolution_clock::now();
	// 	auto exec_time = fpMillisec(nd - _pt).count();
	// 	if (_verbose == true) {
	// #ifdef __ANDROID__
	// 		LOG(ANDROID_LOG_DEBUG, tag.c_str(), "[%s] %s , %s ms", part.c_str(), process.c_str(), std::to_string(exec_time).c_str());
	// #else
	// 		std::cout << tag << " : " << "[" << part << "]  " << process << " , " << exec_time << " ms" << std::endl;
	// #endif // __ANDROID__
	// 	}
}

//void Algo::configShow(void) {
//	std::cout << "=======================================\n";
//	std::cout << "Algorithm Configuration" << std::endl;
//	std::cout << "=======================================\n";
//	std::cout << "Verbose         : " << _verbose << std::endl;
//	std::cout << "FD input size   : " << _fd_size << std::endl;
//	std::cout << "FR input size   : " << _fr_size << std::endl;
//	std::cout << "Score thres     : " << _score_thres << std::endl;
//	std::cout << "NMS thres       : " << _nms_score_thres << std::endl;
//	std::cout << "NMS IOU thres   : " << _nms_iou_thres << std::endl;
//	std::cout << "Fix Alginment   : " << _fix_alignment << std::endl;
//	std::cout << "Warping border  : " << _border_type << std::endl;
//	std::cout << "\n\n\n";
//}

void Algo::setConfigVerbose(bool verbose) {
	_verbose = verbose;
}

std::vector<int> Algo::getDynamicInputSize(cv::Size sz_in) {
	// FD Net convolution has 5 stage of 2x donwsample
	// which is equivalent to 2^5 = 32 multiplier
	// Rounding up the size with the multiplier
	// ensure the convolution to not producing non int value
	int offset_right = 0, offset_bot = 0;
	int mult = 32;
	int base_w = round(sz_in.width / mult);
	int w_out = base_w * mult;
	if (w_out != sz_in.width) {
		w_out += mult;
		offset_right = w_out - sz_in.width;
	}

	int base_h = round(sz_in.height / mult);
	int h_out = base_h * mult;
	if (h_out != sz_in.height) {
		h_out += mult;
		offset_bot = h_out - sz_in.height;
	}

	cv::Size out_sz{ w_out, h_out };

	if (_verbose) {
#ifdef __ANDROID__
		LOG(ANDROID_LOG_DEBUG, "Input size (old --> new)", "%d x %d",
			out_sz.height, out_sz.width);
#else
		std::cout << "Input size (old --> new) : "
			<< sz_in << " --> " << out_sz << std::endl;
#endif
	}

	std::vector<int> sz_and_off = { h_out, w_out, offset_bot, offset_right };

	return sz_and_off;
}

void Algo::setConfigFDSize(cv::Size fd_size) {
	_fd_size = fd_size;
}

void Algo::setConfigSize(cv::Size fd_size, cv::Size fr_size) {
	_fd_size = fd_size;
	_fr_size = fr_size;
}

void Algo::setConfigDetection(float score, float nms, float nms_iou) {
	_score_thres = score;
	_nms_score_thres = nms;
	_nms_iou_thres = nms_iou;
}

void Algo::setConfigWarping(bool fix_alignment, int border_type, int warp_type) {
	_fix_alignment = fix_alignment;
	_border_type = border_type;
	_warp_type = warp_type;
}

void Algo::setConfigScoreTh(float score) {
	_score_thres = score;
	_nms_score_thres = score;
}

void Algo::setConfigIouTh(float iou_threshold) {
	_nms_iou_thres = iou_threshold;
}

void Algo::getConfigScoreTh(float& score) {
	score = _score_thres;
	score = _nms_score_thres;
}

void Algo::getConfigIouTh(float& iou_threshold) {
	iou_threshold = _nms_iou_thres;
}

// set the number of max face.
void Algo::setConfigNumberofFace(int NumberofFace)
{
	_top_k_det = NumberofFace;
}

// set the number of max face.
void Algo::getConfigNumberofFace(int& NumberofFace)
{
	NumberofFace = _top_k_det;
}

void Algo::setConfigSortingType(int sorting_type)
{
	if (sorting_type > 2)
		_face_sorting_type = 0;
	_face_sorting_type = sorting_type;
}

void Algo::getConfigSortingType(int& sorting_type)
{
	sorting_type = _face_sorting_type;
}

void Algo::setConfigSortingType_multi(int sorting_type)
{
	if (sorting_type > 2)
		_multi_face_sorting_type = 0;
	_multi_face_sorting_type = sorting_type;
}

void Algo::getConfigSortingType_multi(int& sorting_type)
{
	sorting_type = _multi_face_sorting_type;
}

void Algo::Show_config(void) {
	std::cout << "=======================================\n";
	std::cout << "Algorithm Configuration" << std::endl;
	std::cout << "=======================================\n";
	std::cout << "Verbose         : " << _verbose << std::endl;
	std::cout << "FD input size   : " << _fd_size << std::endl;
	std::cout << "FR input size   : " << _fr_size << std::endl;
	std::cout << "Score thres     : " << _score_thres << std::endl;
	std::cout << "NMS thres       : " << _nms_score_thres << std::endl;
	std::cout << "NMS IOU thres   : " << _nms_iou_thres << std::endl;
	std::cout << "Fix Alginment   : " << _fix_alignment << std::endl;
	std::cout << "Warping border  : " << _border_type << std::endl;
	std::cout << "\n\n\n";
}



int Algo::uninitFRNet() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {

		int status = infernet[i]->uninit_FR();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFRNet_mask() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FR_mask();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFRNet_no_mask() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FR_no_mask();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFDNet() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FD();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFDNet_landscape() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FD_landscape();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFDNet2() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FD2();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFDNet_mask() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FD_mask();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFDNet2_mask() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FD2_mask();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFDNet_yolo_landscape() {
	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FD_yolo_landscape();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitPoseNet() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_POSE();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFASNet() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FAS();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFASNet_NIR() {

	if (infernet.size() == 0)
		return -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FAS_NIR();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitMSNet() {

	if (infernet.size() == 0)
		return  -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_MS();

		if (status < 0)
			return -1;
	}

	return 1;
}

int Algo::uninitFAMNet() {

	if (infernet.size() == 0)
		return  -1;

	for (int i = 0; i < infernet.size(); i++) {
		int status = infernet[i]->uninit_FAM();

		if (status < 0)
			return -1;
	}

	return 1;
}
